// Script to create a test monitor
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function createTestMonitor() {
  console.log('Creating a test monitor...');

  try {
    // First, get existing companies and users
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('id')
      .limit(1);

    if (companiesError) {
      throw new Error(`Error fetching companies: ${companiesError.message}`);
    }

    if (!companies || companies.length === 0) {
      throw new Error('No companies found in the database. Cannot create a monitor without a company.');
    }

    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (usersError) {
      throw new Error(`Error fetching users: ${usersError.message}`);
    }

    if (!users || users.length === 0) {
      throw new Error('No users found in the database. Cannot create a monitor without a user.');
    }

    // Create a test monitor
    const testMonitor = {
      name: 'Test Monitor ' + new Date().toISOString().slice(0, 19).replace('T', ' '),
      target: 'https://www.google.com',
      type: 'http',
      interval: 5,
      timeout: 30,
      active: true,
      company_id: companies[0].id,
      user_id: users[0].id
    };

    console.log('Creating monitor with the following data:');
    console.log(testMonitor);

    const { data: newMonitor, error: createError } = await supabase
      .from('monitors')
      .insert(testMonitor)
      .select();

    if (createError) {
      throw new Error(`Error creating monitor: ${createError.message}`);
    }

    console.log('Test monitor created successfully!');
    console.log('New monitor details:');
    console.log(newMonitor);

    // Verify the monitor was created
    const { data: allMonitors, error: verifyError } = await supabase
      .from('monitors')
      .select('*');

    if (verifyError) {
      throw new Error(`Error verifying monitors: ${verifyError.message}`);
    }

    console.log(`\nTotal monitors in database: ${allMonitors.length}`);

  } catch (error) {
    console.error('Error:', error.message);
  }
}

createTestMonitor();
