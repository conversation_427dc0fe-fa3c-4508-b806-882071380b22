// <PERSON>ript to check the data type of the active field in monitors
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkActiveField() {
  console.log('Checking active field data types...');

  try {
    // Get all monitors
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*');

    if (error) {
      throw error;
    }

    console.log(`Found ${monitors.length} total monitors in the database`);

    // Display data types
    console.log('\nMonitor active field values and types:');
    monitors.forEach(monitor => {
      console.log(`- ${monitor.name}: active = ${monitor.active} (${typeof monitor.active})`);
    });

    // Update monitors with string 'true' to boolean true
    const stringTrueMonitors = monitors.filter(m => monitor.active === 'true');
    if (stringTrueMonitors.length > 0) {
      console.log(`\nFound ${stringTrueMonitors.length} monitors with string 'true'. Updating to boolean...`);

      const { error: updateError } = await supabase
        .from('monitors')
        .update({ active: true })
        .in('id', stringTrueMonitors.map(m => m.id));

      if (updateError) {
        throw updateError;
      }

      console.log('Successfully updated string values to boolean!');
    }

    // Fix any other issues
    const inactiveMonitors = monitors.filter(m => m.active !== true);
    if (inactiveMonitors.length > 0) {
      console.log(`\nFound ${inactiveMonitors.length} inactive monitors. Do you want to activate them? (y/n)`);
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });

      readline.question('> ', async (answer) => {
        if (answer.toLowerCase() === 'y') {
          const { error: activateError } = await supabase
            .from('monitors')
            .update({ active: true })
            .in('id', inactiveMonitors.map(m => m.id));

          if (activateError) {
            console.error('Error activating monitors:', activateError.message);
          } else {
            console.log('Successfully activated all monitors!');
          }
        }

        readline.close();
      });
    }

  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkActiveField();
