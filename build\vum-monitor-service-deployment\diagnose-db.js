// Database diagnostic script
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function diagnoseDatabase() {
  console.log('=== DATABASE DIAGNOSTIC TOOL ===');
  console.log(`Supabase URL: ${SUPABASE_URL}`);
  console.log(`Supabase Key: ${SUPABASE_KEY.substring(0, 10)}...${SUPABASE_KEY.substring(SUPABASE_KEY.length - 5)}`);

  // Step 1: List all tables
  console.log('\n=== STEP 1: LISTING ALL TABLES ===');
  try {
    const { data: tables, error } = await supabase.rpc('get_tables');

    if (error) {
      console.log('Error fetching tables using RPC:', error.message);
      console.log('Trying alternative approach...');

      // Try direct query to information_schema
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');

      if (schemaError) {
        console.log('Error fetching tables from information_schema:', schemaError.message);
        console.log('Trying common table names directly...');

        // Try common table names
        const commonTables = ['monitors', 'monitor', 'sites', 'endpoints', 'checks', 'urls'];
        for (const tableName of commonTables) {
          try {
            const { data, error: tableError } = await supabase
              .from(tableName)
              .select('count(*)', { count: 'exact', head: true });

            if (!tableError) {
              console.log(`Table '${tableName}' exists!`);
            }
          } catch (e) {
            // Table doesn't exist or can't be accessed
          }
        }
      } else {
        console.log('Tables found in information_schema:');
        schemaData.forEach(table => {
          console.log(`- ${table.table_name}`);
        });
      }
    } else {
      console.log('Tables found:');
      tables.forEach(table => {
        console.log(`- ${table.table_name}`);
      });
    }
  } catch (error) {
    console.log('Error listing tables:', error.message);
  }

  // Step 2: Try to find monitor tables
  console.log('\n=== STEP 2: SEARCHING FOR MONITOR TABLES ===');
  const possibleTables = ['monitors', 'monitor', 'sites', 'endpoints', 'checks', 'urls'];

  for (const tableName of possibleTables) {
    try {
      console.log(`Checking table '${tableName}'...`);
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(5);

      if (error) {
        console.log(`Error accessing table '${tableName}': ${error.message}`);
      } else if (data && data.length > 0) {
        console.log(`Found ${data.length} records in table '${tableName}'!`);
        console.log('Sample record:');
        console.log(data[0]);

        // Check if this looks like a monitor table
        const hasMonitorFields = ['url', 'target', 'endpoint', 'interval', 'timeout'].some(
          field => Object.keys(data[0]).some(key => key.includes(field))
        );

        if (hasMonitorFields) {
          console.log(`Table '${tableName}' appears to be a monitor table!`);

          // Try to get all records
          const { data: allData, error: allError } = await supabase
            .from(tableName)
            .select('*');

          if (!allError) {
            console.log(`Total records in '${tableName}': ${allData.length}`);

            // Check active status
            const activeRecords = allData.filter(record => record.active === true).length;
            const inactiveRecords = allData.filter(record => record.active === false).length;
            const nullActiveRecords = allData.filter(record => record.active === null).length;
            const undefinedActiveRecords = allData.filter(record => record.active === undefined).length;

            console.log(`Records with active=true: ${activeRecords}`);
            console.log(`Records with active=false: ${inactiveRecords}`);
            console.log(`Records with active=null: ${nullActiveRecords}`);
            console.log(`Records with active=undefined: ${undefinedActiveRecords}`);

            // Show all records
            console.log('\nAll records:');
            allData.forEach((record, index) => {
              console.log(`\nRecord #${index + 1}:`);
              Object.entries(record).forEach(([key, value]) => {
                console.log(`${key}: ${value === null ? 'null' : value} (${typeof value})`);
              });
            });
          }
        }
      } else {
        console.log(`Table '${tableName}' exists but has no records.`);
      }
    } catch (error) {
      console.log(`Error checking table '${tableName}': ${error.message}`);
    }
  }

  // Step 3: Check monitor_history table
  console.log('\n=== STEP 3: CHECKING MONITOR_HISTORY TABLE ===');
  try {
    const { data, error } = await supabase
      .from('monitor_history')
      .select('*')
      .limit(5);

    if (error) {
      console.log(`Error accessing 'monitor_history' table: ${error.message}`);
    } else if (data && data.length > 0) {
      console.log(`Found ${data.length} records in 'monitor_history' table!`);
      console.log('Sample record:');
      console.log(data[0]);

      // Get unique monitor IDs
      const { data: allData, error: allError } = await supabase
        .from('monitor_history')
        .select('monitor_id');

      if (!allError) {
        const monitorIds = [...new Set(allData.map(record => record.monitor_id))];
        console.log(`Unique monitor IDs in history: ${monitorIds.join(', ')}`);

        // Try to find these monitors
        for (const monitorId of monitorIds) {
          for (const tableName of possibleTables) {
            try {
              const { data: monitorData, error: monitorError } = await supabase
                .from(tableName)
                .select('*')
                .eq('id', monitorId)
                .limit(1);

              if (!monitorError && monitorData && monitorData.length > 0) {
                console.log(`Found monitor ${monitorId} in table '${tableName}'!`);
                console.log(monitorData[0]);
                break;
              }
            } catch (e) {
              // Table doesn't exist or can't be accessed
            }
          }
        }
      }
    } else {
      console.log(`'monitor_history' table exists but has no records.`);
    }
  } catch (error) {
    console.log(`Error checking 'monitor_history' table: ${error.message}`);
  }

  // Step 4: Test inserting a record
  console.log('\n=== STEP 4: TESTING INSERT PERMISSIONS ===');

  // First, try to find a valid monitor ID
  let testMonitorId = null;
  for (const tableName of possibleTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .limit(1);

      if (!error && data && data.length > 0) {
        testMonitorId = data[0].id;
        console.log(`Found monitor ID ${testMonitorId} in table '${tableName}' for testing.`);
        break;
      }
    } catch (e) {
      // Table doesn't exist or can't be accessed
    }
  }

  if (testMonitorId) {
    try {
      const testRecord = {
        monitor_id: testMonitorId,
        status: true,
        response_time: 123,
        error_message: 'Test record - please ignore',
        timestamp: new Date().toISOString()
      };

      console.log('Attempting to insert test record:');
      console.log(testRecord);

      const { error } = await supabase
        .from('monitor_history')
        .insert(testRecord);

      if (error) {
        console.log('Error inserting test record:', error.message);
      } else {
        console.log('Successfully inserted test record!');
      }
    } catch (error) {
      console.log('Error inserting test record:', error.message);
    }
  } else {
    console.log('Could not find a monitor ID for testing insert permissions.');
  }

  console.log('\n=== DIAGNOSTIC COMPLETE ===');
}

diagnoseDatabase().catch(error => {
  console.error('Unhandled error:', error);
});
