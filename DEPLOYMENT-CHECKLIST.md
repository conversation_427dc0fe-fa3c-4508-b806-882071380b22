# VUM Monitor Service - Deployment Checklist

## Pre-Deployment Requirements

### Server Requirements
- [ ] Node.js 14.x or higher installed
- [ ] npm package manager available
- [ ] Internet connection for package installation
- [ ] Sufficient disk space (minimum 100MB)
- [ ] Port access for database connections

### Supabase Setup
- [ ] Supabase project created
- [ ] Service role key obtained
- [ ] Database password available
- [ ] RLS policies configured (if needed)
- [ ] Monitor tables created (or will be created during setup)

## Deployment Steps

### 1. Build Package
- [ ] Run `npm run build` on source machine
- [ ] Verify build completed successfully
- [ ] Check that zip file was created in `build/` directory

### 2. Transfer to Target Server
- [ ] Copy deployment package to target server
- [ ] Extract package if using zip file
- [ ] Verify all files are present

### 3. Server Setup
- [ ] Navigate to deployment directory
- [ ] Run setup script:
  - Windows: `setup.bat`
  - Linux/Mac: `./setup.sh`
- [ ] Verify dependencies installed successfully

### 4. Configuration
- [ ] Copy `.env.example` to `.env`
- [ ] Edit `.env` file with your settings:
  - [ ] `SUPABASE_URL`
  - [ ] `SUPABASE_KEY` (service role key)
  - [ ] `DB_HOST`
  - [ ] `DB_PASSWORD`
  - [ ] `DB_USER` (usually 'postgres')
  - [ ] Email settings (if using notifications)
- [ ] Run `npm run check-env` to verify configuration

### 5. Database Setup
- [ ] Run `npm run create-tables` to create required tables
- [ ] Verify tables were created successfully
- [ ] Test database connection with `npm run diagnose`

### 6. Initial Testing
- [ ] Run `npm run status` to check service status
- [ ] Test monitor creation (if applicable)
- [ ] Run a manual check to verify functionality
- [ ] Check logs in `logs/` directory

### 7. Service Startup
- [ ] Start the service:
  - Manual: `npm start` or `./start.sh`
  - Windows Service: `npm run install-win-service`
- [ ] Verify service is running
- [ ] Check logs for any errors

### 8. Optional: Notification Setup
- [ ] Configure email settings in `.env`
- [ ] Start notification listener: `npm run start-notifications`
- [ ] Test email notifications (if configured)

## Post-Deployment Verification

### Service Health Checks
- [ ] Service is running without errors
- [ ] Logs show successful monitor checks
- [ ] Database connections are stable
- [ ] No critical errors in error logs

### Functional Testing
- [ ] Monitors are being checked at configured intervals
- [ ] Manual checks work correctly
- [ ] Status changes are detected and recorded
- [ ] Notifications are sent (if configured)

### Performance Monitoring
- [ ] Monitor response times are reasonable
- [ ] Memory usage is stable
- [ ] CPU usage is acceptable
- [ ] Disk space is sufficient for logs

## Troubleshooting

### Common Issues
- **Dependencies fail to install**: Check Node.js version and internet connection
- **Database connection fails**: Verify Supabase credentials and network access
- **Service won't start**: Check `.env` configuration and logs
- **Monitors not checking**: Verify monitor configuration and database tables

### Useful Commands
- `npm run check-env` - Verify environment configuration
- `npm run diagnose` - Run database diagnostics
- `npm run status` - Check service status
- `npm run check-monitors` - List all monitors
- `tail -f logs/monitor-service.log` - View live logs (Linux/Mac)

### Log Locations
- Main logs: `logs/monitor-service.log`
- Error logs: `logs/error.log`
- Email logs: `logs/email-notification-listener.log`

## Maintenance

### Regular Tasks
- [ ] Monitor log file sizes and rotate if needed
- [ ] Check service status regularly
- [ ] Update dependencies periodically
- [ ] Backup configuration files

### Updates
- [ ] Stop service before updating
- [ ] Backup current installation
- [ ] Deploy new version
- [ ] Test functionality
- [ ] Restart service

## Security Considerations

- [ ] Secure `.env` file permissions (readable only by service user)
- [ ] Use strong database passwords
- [ ] Limit network access to required ports only
- [ ] Regularly update dependencies for security patches
- [ ] Monitor logs for suspicious activity

## Support

For issues and support:
- GitHub Repository: https://github.com/RayZwankhuizen/VUM-Backend
- Check logs first for error details
- Run diagnostic commands to identify issues
- Verify configuration before reporting problems
