// Script to show all monitors with all their values
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function showAllMonitors() {
  console.log('Fetching all monitors from the database...');

  try {
    // Get all monitors
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*');

    if (error) {
      throw error;
    }

    if (!monitors || monitors.length === 0) {
      console.log('No monitors found in the database.');
      return;
    }

    console.log(`Found ${monitors.length} total monitors in the database.\n`);

    // Display all monitors with all their values
    console.log('=== DETAILED MONITOR INFORMATION ===');
    monitors.forEach((monitor, index) => {
      console.log(`\nMonitor #${index + 1}:`);
      console.log('----------------------------------------');

      // Display all properties
      Object.entries(monitor).forEach(([key, value]) => {
        console.log(`${key}: ${value === null ? 'null' : value} (${typeof value})`);
      });

      console.log('----------------------------------------');
    });

    // Show summary of active status
    const activeMonitors = monitors.filter(m => m.active === true).length;
    const stringTrueMonitors = monitors.filter(m => m.active === 'true').length;
    const numberOneMonitors = monitors.filter(m => m.active === 1).length;
    const stringOneMonitors = monitors.filter(m => m.active === '1').length;
    const nullActiveMonitors = monitors.filter(m => m.active === null).length;
    const falseActiveMonitors = monitors.filter(m => m.active === false).length;
    const stringFalseMonitors = monitors.filter(m => m.active === 'false').length;

    console.log('\n=== ACTIVE STATUS SUMMARY ===');
    console.log(`Monitors with active = true (boolean): ${activeMonitors}`);
    console.log(`Monitors with active = 'true' (string): ${stringTrueMonitors}`);
    console.log(`Monitors with active = 1 (number): ${numberOneMonitors}`);
    console.log(`Monitors with active = '1' (string): ${stringOneMonitors}`);
    console.log(`Monitors with active = null: ${nullActiveMonitors}`);
    console.log(`Monitors with active = false (boolean): ${falseActiveMonitors}`);
    console.log(`Monitors with active = 'false' (string): ${stringFalseMonitors}`);

    // Show table structure
    console.log('\n=== TABLE STRUCTURE ===');
    try {
      const { data: tableInfo, error: tableError } = await supabase
        .rpc('get_table_info', { table_name: 'monitors' });

      if (tableError) {
        console.log('Could not fetch table structure:', tableError.message);
      } else if (tableInfo) {
        console.log(tableInfo);
      }
    } catch (e) {
      console.log('Error fetching table structure:', e.message);
    }

    // Offer to fix active status
    console.log('\nDo you want to set all monitors to active=true? (y/n)');
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });

    readline.question('> ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        try {
          const { error: updateError } = await supabase
            .from('monitors')
            .update({ active: true })
            .is('id', 'not.null'); // Update all rows

          if (updateError) {
            console.error('Error updating monitors:', updateError.message);
          } else {
            console.log('Successfully set all monitors to active=true!');
          }
        } catch (e) {
          console.error('Error:', e.message);
        }
      }

      readline.close();
    });

  } catch (error) {
    console.error('Error:', error.message);
  }
}

showAllMonitors();
