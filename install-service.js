// Script to install the monitor service as a Windows service
const { Service } = require('node-windows');
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'VUM Monitor Service',
  description: 'Background service for VUM monitoring system (Concurrent)',
  script: path.join(__dirname, 'concurrent-monitor-service.js'),
  nodeOptions: [],
  // Allow the service to restart on failure
  restartOnFailure: true,
  // Allow 10 restarts within a 60 second period
  maxRestarts: 10,
  maxRestartTime: 60
});

// Listen for the "install" event
svc.on('install', () => {
  console.log('Service installed successfully!');
  // Start the service
  svc.start();
});

// Listen for the "start" event
svc.on('start', () => {
  console.log('Service started successfully!');
});

// Listen for the "error" event
svc.on('error', (err) => {
  console.error('Error installing service:', err);
});

// Install the service
console.log('Installing service...');
svc.install();
