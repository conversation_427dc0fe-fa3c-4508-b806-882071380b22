/**
 * <PERSON><PERSON><PERSON> to test if the service role key works
 *
 * This script tests if the Supabase service key is valid and has the necessary permissions
 * to access the monitors table and bypass Row Level Security (RLS).
 *
 * Usage: node test-service-key.js
 */
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const MONITOR_TABLE = process.env.MONITOR_TABLE || 'monitors';

// Check if required environment variables are set
if (!SUPABASE_URL) {
  console.error('ERROR: Required environment variable SUPABASE_URL is not set.');
  console.error('Please set this variable in your .env file.');
  process.exit(1);
}

if (!SUPABASE_KEY) {
  console.error('ERROR: Required environment variable SUPABASE_KEY is not set.');
  console.error('Please set this variable in your .env file.');
  console.error('IMPORTANT: SUPABASE_KEY must be the service_role key from Supabase.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function testServiceKey() {
  console.log('=== SERVICE KEY TEST ===');
  console.log(`Supabase URL: ${SUPABASE_URL}`);
  console.log(`Key type: ${SUPABASE_KEY.includes('role=anon') ? 'anon key' : 'service role key'}`);
  console.log(`Monitor Table: ${MONITOR_TABLE}`);

  // Test connection
  console.log('\nTesting connection to Supabase...');
  try {
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error(`❌ ERROR: Failed to connect to Supabase: ${sessionError.message}`);
      return;
    }

    console.log('✅ SUCCESS: Connected to Supabase successfully.');

    // Try to read from the monitors table
    console.log('\nAttempting to read from monitors table...');
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('*');

    if (error) {
      console.error(`❌ ERROR: ${error.message}`);
      console.error('The key does not have permission to read from the monitors table.');
      console.error('Please use a service role key or create an RLS policy to allow access.');
      return;
    }

    console.log(`✅ SUCCESS! Found ${data.length} monitors.`);

    // Test if we can access monitors from all companies (RLS bypass)
    console.log('\nTesting if service key can bypass RLS...');

    // Get distinct company_ids from monitors table
    const { data: companyData, error: companyError } = await supabase
      .from(MONITOR_TABLE)
      .select('company_id')
      .limit(10);

    if (companyError) {
      console.error(`❌ ERROR: Failed to get company IDs: ${companyError.message}`);
      return;
    }

    if (!companyData || companyData.length === 0) {
      console.log('⚠️ WARNING: No monitors found in the database.');
      return;
    }

    // Get unique company IDs
    const companyIds = [...new Set(companyData.map(m => m.company_id).filter(id => id))];
    console.log(`Found monitors from ${companyIds.length} different companies.`);

    // If there's only one company, we can't really test RLS bypass
    if (companyIds.length <= 1) {
      console.log('⚠️ WARNING: Only one company found, cannot fully test RLS bypass.');

      if (data.length > 0) {
        console.log('\nMonitors:');
        data.slice(0, 5).forEach((monitor, index) => {
          console.log(`\nMonitor #${index + 1}:`);
          console.log(`ID: ${monitor.id}`);
          console.log(`Name: ${monitor.name}`);
          console.log(`Type: ${monitor.type}`);
          console.log(`Target: ${monitor.target}`);
          console.log(`Interval: ${monitor.interval}`);
          console.log(`Active: ${monitor.active} (${typeof monitor.active})`);
          console.log(`Company ID: ${monitor.company_id}`);
        });

        if (data.length > 5) {
          console.log(`\n... and ${data.length - 5} more monitors`);
        }
      }

      console.log('\n✅ SUCCESS: The key has permission to read from the monitors table!');
      console.log('You can now run the monitor service with this key.');
      return;
    }

    // Try to access monitors from each company
    let allSuccess = true;
    for (const companyId of companyIds) {
      const { data: companyMonitors, error: companyMonitorsError } = await supabase
        .from(MONITOR_TABLE)
        .select('id, name')
        .eq('company_id', companyId)
        .limit(1);

      if (companyMonitorsError) {
        console.error(`❌ ERROR: Failed to access monitors for company ${companyId}: ${companyMonitorsError.message}`);
        allSuccess = false;
      } else {
        console.log(`✅ SUCCESS: Accessed monitors for company ${companyId}`);
      }
    }

    if (allSuccess) {
      console.log('\n✅ SUCCESS: Service key can bypass RLS and access monitors from all companies.');
      console.log('You can now run the monitor service with this key.');
    } else {
      console.error('\n❌ ERROR: Service key cannot access monitors from all companies.');
      console.error('Please make sure you are using a service role key, not an anon key.');
    }
  } catch (error) {
    console.error(`❌ ERROR: ${error.message}`);
  }
}

testServiceKey().catch(error => {
  console.error('Unhandled error:', error);
});
