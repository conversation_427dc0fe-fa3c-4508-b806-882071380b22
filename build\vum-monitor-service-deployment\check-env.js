// Script to check if the .env file is properly configured
require('dotenv').config();
const fs = require('fs');
const path = require('path');

console.log('Checking environment configuration...');

// Required environment variables
const requiredVars = [
  { name: 'SUPABASE_URL', description: 'Supabase project URL' },
  { name: 'SUPABASE_KEY', description: 'Supabase API key' },
  { name: 'MONITOR_TABLE', description: 'Name of the monitors table', default: 'monitors' },
  { name: 'HISTORY_TABLE', description: 'Name of the monitor history table', default: 'monitor_history' },
  { name: 'NOTIFICATION_TABLE', description: 'Name of the notifications table', default: 'notifications' },
  { name: 'CHECK_INTERVAL', description: 'Interval to check for monitor updates (ms)', default: '60000' },
  { name: 'MAX_CONCURRENT_CHECKS', description: 'Maximum number of concurrent checks', default: '10' },
  { name: 'LOG_LEVEL', description: 'Logging level', default: 'info' }
];

// Optional environment variables
const optionalVars = [
  { name: 'RESEND_API_KEY', description: 'API key for Resend email service (required for email notifications)' },
  { name: 'DB_HOST', description: 'Database host (required for notification listener)' },
  { name: 'DB_PORT', description: 'Database port (required for notification listener)', default: '5432' },
  { name: 'DB_NAME', description: 'Database name (required for notification listener)', default: 'postgres' },
  { name: 'DB_USER', description: 'Database user (required for notification listener)', default: 'postgres' },
  { name: 'DB_PASSWORD', description: 'Database password (required for notification listener)' }
];

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.error('\x1b[31mERROR: .env file not found!\x1b[0m');
  console.log('Please create a .env file by copying .env.example:');
  console.log('  cp .env.example .env');
  console.log('Then edit the .env file to set your configuration values.');
  process.exit(1);
}

// Check required variables
let missingRequired = false;
console.log('\n\x1b[1mRequired Environment Variables:\x1b[0m');
for (const variable of requiredVars) {
  const value = process.env[variable.name];
  if (!value) {
    console.log(`  \x1b[31m✗ ${variable.name}: Missing\x1b[0m (${variable.description})`);
    if (variable.default) {
      console.log(`    Default value will be used: ${variable.default}`);
    } else {
      missingRequired = true;
    }
  } else {
    const displayValue = variable.name.includes('KEY') || variable.name.includes('PASSWORD') 
      ? '********' 
      : value;
    console.log(`  \x1b[32m✓ ${variable.name}:\x1b[0m ${displayValue}`);
  }
}

// Check optional variables
console.log('\n\x1b[1mOptional Environment Variables:\x1b[0m');
for (const variable of optionalVars) {
  const value = process.env[variable.name];
  if (!value) {
    console.log(`  \x1b[33m○ ${variable.name}: Not set\x1b[0m (${variable.description})`);
    if (variable.default) {
      console.log(`    Default value will be used: ${variable.default}`);
    }
  } else {
    const displayValue = variable.name.includes('KEY') || variable.name.includes('PASSWORD') 
      ? '********' 
      : value;
    console.log(`  \x1b[32m✓ ${variable.name}:\x1b[0m ${displayValue}`);
  }
}

// Check for notification-specific variables if running notification listener
if (process.argv.includes('--check-notifications')) {
  console.log('\n\x1b[1mChecking Notification Listener Requirements:\x1b[0m');
  const notificationVars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
  let missingNotification = false;
  
  for (const varName of notificationVars) {
    if (!process.env[varName]) {
      console.log(`  \x1b[31m✗ ${varName}: Missing\x1b[0m (required for notification listener)`);
      missingNotification = true;
    }
  }
  
  if (missingNotification) {
    console.log('\n\x1b[33mWarning: Some required variables for the notification listener are missing.\x1b[0m');
    console.log('The notification listener may not function correctly.');
  } else {
    console.log('\n\x1b[32mAll notification listener variables are set.\x1b[0m');
  }
}

// Check for email-specific variables if running email service
if (process.argv.includes('--check-email')) {
  console.log('\n\x1b[1mChecking Email Service Requirements:\x1b[0m');
  
  if (!process.env.RESEND_API_KEY) {
    console.log('  \x1b[31m✗ RESEND_API_KEY: Missing\x1b[0m (required for email notifications)');
    console.log('\n\x1b[33mWarning: RESEND_API_KEY is missing. Email notifications will not work.\x1b[0m');
  } else {
    console.log('  \x1b[32m✓ RESEND_API_KEY: Set\x1b[0m');
    console.log('\n\x1b[32mEmail service configuration is complete.\x1b[0m');
  }
}

// Final status
if (missingRequired) {
  console.log('\n\x1b[31mERROR: Some required environment variables are missing.\x1b[0m');
  console.log('Please update your .env file with the missing values.');
  process.exit(1);
} else {
  console.log('\n\x1b[32mEnvironment configuration check passed!\x1b[0m');
  console.log('You can now run the monitor service using npm commands.');
}
