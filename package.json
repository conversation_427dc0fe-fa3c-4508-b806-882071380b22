{"name": "vum-monitor-service", "version": "1.0.0", "description": "Background service for VUM monitoring system", "main": "concurrent-monitor-service.js", "scripts": {"start": "node concurrent-monitor-service.js", "dev": "nodemon concurrent-monitor-service.js", "start-notifications": "node notification-listener.js", "dev-notifications": "nodemon notification-listener.js", "diagnose": "node diagnose-db.js", "check-monitors": "node check-monitors-direct.js", "find-table": "node find-monitor-table.js", "check-rls": "node check-rls.js", "test-key": "node test-service-key.js", "check-website": "node check-website.js", "check-monitor": "node check-monitor-by-id.js", "check-history": "node check-recent-history.js", "direct-check": "node direct-check-monitor.js", "manual-check": "node manual-check.js", "concurrent-manual-check": "node concurrent-manual-check.js", "check-now": "node test-check-now.js", "check-now-monitor": "node test-check-now.js --id=", "create-tables": "node create-monitor-table.js", "check-env": "node check-env.js", "check-env-notifications": "node check-env.js --check-notifications", "check-env-email": "node check-env.js --check-email", "prestart": "npm run check-env", "prestart-notifications": "npm run check-env-notifications", "prestart-email-listener": "npm run check-env-email", "test-email": "node test-email-notification.js", "send-email": "node direct-email-sender.js", "start-email-listener": "node email-notification-listener.js", "install-win-service": "node install-service.js", "uninstall-win-service": "node uninstall-service.js", "install-notification-service": "node install-notification-service.js", "uninstall-notification-service": "node uninstall-notification-service.js", "install-email-service": "node install-email-service.js", "uninstall-email-service": "node uninstall-email-service.js", "status": "node check-service-status.js", "build": "node build-deployment.js", "setup": "npm install && npm run check-env"}, "dependencies": {"@supabase/supabase-js": "^2.39.7", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^5.1.0", "node-cron": "^3.0.3", "node-windows": "^1.0.0-beta.8", "pg": "^8.11.3", "winston": "^3.12.0"}, "devDependencies": {"nodemon": "^3.1.0"}}