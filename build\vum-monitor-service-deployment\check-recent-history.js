// <PERSON>ript to check recent entries in the monitor_history table
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to get recent history entries
async function getRecentHistory(minutes = 5) {
  try {
    // Calculate the timestamp for X minutes ago
    const minutesAgo = new Date();
    minutesAgo.setMinutes(minutesAgo.getMinutes() - minutes);
    
    // Query the monitor_history table for recent entries
    const { data, error } = await supabase
      .from('monitor_history')
      .select('*, monitors(name)')
      .gte('timestamp', minutesAgo.toISOString())
      .order('timestamp', { ascending: false });
    
    if (error) {
      throw error;
    }
    
    if (!data || data.length === 0) {
      console.log(`No monitor history entries found in the last ${minutes} minutes.`);
      return;
    }
    
    console.log(`Found ${data.length} monitor history entries in the last ${minutes} minutes:`);
    console.log('---------------------------------------------------');
    
    // Group entries by monitor
    const entriesByMonitor = {};
    
    data.forEach(entry => {
      const monitorName = entry.monitors?.name || entry.monitor_id;
      if (!entriesByMonitor[monitorName]) {
        entriesByMonitor[monitorName] = [];
      }
      entriesByMonitor[monitorName].push(entry);
    });
    
    // Display entries by monitor
    Object.keys(entriesByMonitor).forEach(monitorName => {
      const entries = entriesByMonitor[monitorName];
      console.log(`Monitor: ${monitorName}`);
      
      entries.forEach(entry => {
        const timestamp = new Date(entry.timestamp).toLocaleString();
        const status = typeof entry.status === 'boolean' 
          ? (entry.status ? 'UP' : 'DOWN')
          : entry.status;
        
        console.log(`  ${timestamp} - Status: ${status}, Response time: ${entry.response_time}ms`);
        if (entry.error_message) {
          console.log(`    Error: ${entry.error_message}`);
        }
      });
      
      console.log('---------------------------------------------------');
    });
  } catch (error) {
    console.error(`Error getting recent history: ${error.message}`);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const minutes = args.find(arg => arg.startsWith('--minutes='))?.split('=')[1] || 5;

// Run the function
getRecentHistory(parseInt(minutes))
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
