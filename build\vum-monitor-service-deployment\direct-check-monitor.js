// <PERSON>ript to directly check a monitor and save the result
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to check a monitor
async function checkMonitor(monitorId) {
  try {
    // Get the monitor
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('*')
      .eq('id', monitorId)
      .single();
    
    if (monitorError) {
      throw new Error(`Failed to get monitor: ${monitorError.message}`);
    }
    
    if (!monitor) {
      throw new Error(`Monitor with ID ${monitorId} not found`);
    }
    
    console.log(`Checking monitor: ${monitor.name} (${monitor.target})`);
    
    // Perform the check
    const startTime = Date.now();
    let status = false;
    let responseTime = null;
    let errorMessage = null;
    
    try {
      switch (monitor.type) {
        case 'http':
          // HTTP check
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);
          
          try {
            const response = await axios.get(monitor.target, {
              signal: controller.signal,
              timeout: monitor.timeout * 1000,
              validateStatus: null // Don't throw on non-2xx responses
            });
            
            clearTimeout(timeoutId);
            status = response.status >= 200 && response.status < 300;
            responseTime = Date.now() - startTime;
            
            if (!status) {
              errorMessage = `HTTP status: ${response.status}`;
            }
          } catch (error) {
            clearTimeout(timeoutId);
            throw error;
          }
          break;
          
        case 'ping':
          // Simple ping check (using HTTP HEAD request as a proxy)
          try {
            const response = await axios.head(monitor.target, {
              timeout: monitor.timeout * 1000,
              validateStatus: null
            });
            
            status = response.status >= 200 && response.status < 300;
            responseTime = Date.now() - startTime;
            
            if (!status) {
              errorMessage = `Ping failed with status: ${response.status}`;
            }
          } catch (error) {
            throw new Error(`Ping failed: ${error.message}`);
          }
          break;
          
        case 'port':
          // Port check (simplified - in a real implementation, you'd use a proper TCP check)
          try {
            // Parse the target (format: host|port1,port2,port3)
            const [host, portsStr] = monitor.target.split('|');
            const ports = portsStr.split(',').map(p => parseInt(p.trim()));
            
            // For simplicity, we'll just check if the host is reachable
            const response = await axios.head(`https://${host}`, {
              timeout: monitor.timeout * 1000,
              validateStatus: null
            });
            
            status = response.status >= 200 && response.status < 300;
            responseTime = Date.now() - startTime;
            
            if (!status) {
              errorMessage = `Host unreachable: ${response.status}`;
            }
          } catch (error) {
            throw new Error(`Port check failed: ${error.message}`);
          }
          break;
          
        default:
          throw new Error(`Unsupported monitor type: ${monitor.type}`);
      }
    } catch (error) {
      status = false;
      errorMessage = `Error: ${error.message}`;
      console.error(`Check failed: ${error.message}`);
    }
    
    // Save the check result
    const checkResult = {
      monitor_id: monitor.id,
      status,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };
    
    console.log(`Check result: ${status ? 'UP' : 'DOWN'} (${responseTime}ms)`);
    if (errorMessage) {
      console.log(`Error: ${errorMessage}`);
    }
    
    // Insert the result into the database
    const { error: insertError } = await supabase
      .from('monitor_history')
      .insert(checkResult);
    
    if (insertError) {
      throw new Error(`Failed to save check result: ${insertError.message}`);
    }
    
    console.log('Check result saved to database');
    
    return {
      monitor_id: monitor.id,
      name: monitor.name,
      status,
      response_time: responseTime,
      error_message: errorMessage
    };
  } catch (error) {
    console.error(`Error checking monitor: ${error.message}`);
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];

if (!monitorId) {
  console.error('Please provide a monitor ID with --id=YOUR_MONITOR_ID');
  process.exit(1);
}

// Run the check
checkMonitor(monitorId)
  .then(result => {
    console.log('Check completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  });
