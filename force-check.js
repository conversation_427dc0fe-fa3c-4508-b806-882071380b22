// <PERSON>ript to force a monitor check by updating the last check timestamp
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to log a message to the monitor service log
function logToMonitorService(message, level = 'INFO') {
  try {
    const logDir = path.join(__dirname, 'logs');
    const logFile = path.join(logDir, 'monitor-service.log');

    // Create logs directory if it doesn't exist
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Format the log message
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] [DIRECT-CHECK] ${message}\n`;

    // Append to the log file
    fs.appendFileSync(logFile, logMessage);

    // Also log to console
    console.log(`[${level}] ${message}`);
  } catch (error) {
    console.error(`Error writing to log file: ${error.message}`);
  }
}

// Function to force a check by updating the timestamp
async function forceCheck(monitorId) {
  try {
    // If no monitor ID is provided, update all active monitors
    if (!monitorId) {
      // Get all active monitors
      const { data: monitors, error: monitorsError } = await supabase
        .from('monitors')
        .select('id, name, interval')
        .eq('active', true);

      if (monitorsError) {
        throw new Error(`Failed to get monitors: ${monitorsError.message}`);
      }

      if (!monitors || monitors.length === 0) {
        console.log('No active monitors found');
        return { success: true, monitorsUpdated: 0 };
      }

      console.log(`Found ${monitors.length} active monitors`);

      // Update each monitor's last check timestamp
      let updatedCount = 0;
      for (const monitor of monitors) {
        try {
          await updateMonitorTimestamp(monitor.id, monitor.name, monitor.interval);
          updatedCount++;
        } catch (error) {
          console.error(`Error updating monitor ${monitor.name}: ${error.message}`);
        }
      }

      console.log(`Successfully updated ${updatedCount} monitors`);
      return { success: true, monitorsUpdated: updatedCount };
    } else {
      // Get the specific monitor
      const { data: monitor, error: monitorError } = await supabase
        .from('monitors')
        .select('id, name, interval')
        .eq('id', monitorId)
        .single();

      if (monitorError) {
        throw new Error(`Failed to get monitor: ${monitorError.message}`);
      }

      if (!monitor) {
        throw new Error(`Monitor with ID ${monitorId} not found`);
      }

      // Update the monitor's last check timestamp
      await updateMonitorTimestamp(monitor.id, monitor.name, monitor.interval);

      console.log(`Successfully updated monitor ${monitor.name}`);
      return { success: true, monitorsUpdated: 1 };
    }
  } catch (error) {
    console.error(`Error forcing check: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Helper function to update a monitor's last check timestamp
async function updateMonitorTimestamp(monitorId, monitorName, interval) {
  try {
    // Get the most recent check for this monitor
    const { data: lastCheck, error: lastCheckError } = await supabase
      .from('monitor_history')
      .select('id, timestamp')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false })
      .limit(1);

    if (lastCheckError) {
      throw new Error(`Failed to get last check: ${lastCheckError.message}`);
    }

    if (!lastCheck || lastCheck.length === 0) {
      console.log(`No previous checks found for monitor ${monitorName}. The monitor service will check it automatically.`);
      return;
    }

    // Calculate a new timestamp that will make the monitor due for checking
    // Set it to (interval + 1) minutes ago to ensure it's checked immediately
    const newTimestamp = new Date();
    newTimestamp.setMinutes(newTimestamp.getMinutes() - (interval + 1));

    // Update the timestamp of the most recent check
    const { error: updateError } = await supabase
      .from('monitor_history')
      .update({ timestamp: newTimestamp.toISOString() })
      .eq('id', lastCheck[0].id);

    if (updateError) {
      throw new Error(`Failed to update timestamp: ${updateError.message}`);
    }

    console.log(`Updated timestamp for monitor ${monitorName} to ${newTimestamp.toISOString()}`);
  } catch (error) {
    console.error(`Error updating timestamp for monitor ${monitorName}: ${error.message}`);
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];

// Run the function
forceCheck(monitorId)
  .then(result => {
    console.log(JSON.stringify(result, null, 2));
    process.exit(0);
  })
  .catch(error => {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  });
