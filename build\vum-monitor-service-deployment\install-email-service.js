// Script to install the email notification listener as a Windows service
const Service = require('node-windows').Service;
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'VUM Email Notification Listener',
  description: 'Vurbis Uptime Monitor Email Notification Listener Service',
  script: path.join(__dirname, 'email-notification-listener.js'),
  nodeOptions: [],
  workingDirectory: __dirname,
  allowServiceLogon: true
});

// Listen for the "install" event
svc.on('install', function() {
  console.log('Service installed successfully!');
  svc.start();
});

// Listen for the "start" event
svc.on('start', function() {
  console.log('Service started successfully!');
});

// Listen for the "error" event
svc.on('error', function(err) {
  console.error('Error installing service:', err);
});

// Install the service
console.log('Installing VUM Email Notification Listener service...');
svc.install();
