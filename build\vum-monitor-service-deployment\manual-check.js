// <PERSON>ript to manually check a monitor
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { checkMonitor } = require('./checker');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];

if (!monitorId) {
  log('ERROR: Monitor ID is required. Use --id=<monitor_id>', 'ERROR');
  console.error('Usage: node manual-check.js --id=<monitor_id>');
  process.exit(1);
}

// Main function
async function main() {
  try {
    log(`Starting manual check for monitor ID: ${monitorId}`);

    // Get the monitor from the database
    const { data: monitor, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('id', monitorId)
      .single();

    if (error) {
      throw new Error(`Failed to get monitor: ${error.message}`);
    }

    if (!monitor) {
      throw new Error(`Monitor with ID ${monitorId} not found`);
    }

    log(`Found monitor: ${monitor.name} (${monitor.type})`);

    // Check the monitor with isManualCheck flag set to true
    const result = await checkMonitor(monitor, true);

    log(`Check completed for ${monitor.name}: ${result.status ? 'UP' : 'DOWN'} (${result.response_time}ms)`);
    
    if (result.error_message) {
      log(`Error: ${result.error_message}`, 'WARN');
    }

    process.exit(0);
  } catch (error) {
    log(`Error performing manual check: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run the main function
main();
