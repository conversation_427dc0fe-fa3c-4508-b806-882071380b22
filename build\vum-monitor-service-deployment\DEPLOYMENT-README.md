# VUM Monitor Service - Production Deployment

This is a minimal, production-ready package of the VUM Monitor Service.

## Quick Start

### Windows
1. Run `setup.bat` to install dependencies
2. Verify `.env` file has your correct settings
3. Run `npm run create-tables` to set up database tables (if needed)
4. Run `start.bat` to start the service

### Linux/Mac
1. Run `./setup.sh` to install dependencies
2. Verify `.env` file has your correct settings
3. Run `npm run create-tables` to set up database tables (if needed)
4. Run `./start.sh` to start the service

## What's Included

This minimal package includes only essential files:
- **Core Service**: concurrent-monitor-service.js, checker.js, database.js, logger.js
- **Notifications**: notification-listener.js
- **Configuration**: .env (your actual config), package.json
- **Setup Tools**: create-monitor-table.js, check-env.js
- **Manual Checks**: concurrent-manual-check.js (for dashboard integration)
- **Service Management**: Windows service install/uninstall scripts

## Available Commands

- `npm start` - Start the monitor service
- `npm run start-notifications` - Start notification listener
- `npm run check-env` - Check environment configuration
- `npm run create-tables` - Create database tables
- `npm run status` - Check service status
- `npm run concurrent-manual-check --id=<monitor_id>` - Manual check for dashboard

## Service Management (Windows)

- `npm run install-win-service` - Install as Windows service
- `npm run uninstall-win-service` - Uninstall Windows service

## Logs

Logs are stored in the `logs/` directory:
- `monitor-service.log` - Main service logs
- `error.log` - Error logs only

## Support

For issues and documentation, visit: https://github.com/RayZwankhuizen/VUM-Backend
