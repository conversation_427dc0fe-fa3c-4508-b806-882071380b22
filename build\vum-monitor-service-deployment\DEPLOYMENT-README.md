# VUM Monitor Service - Deployment Package

This is a deployment-ready package of the VUM Monitor Service.

## Quick Start

### Windows
1. Run `setup.bat` to install dependencies
2. Copy `.env.example` to `.env` and configure your settings
3. Run `npm run create-tables` to set up database tables
4. Run `start.bat` to start the service

### Linux/Mac
1. Run `./setup.sh` to install dependencies
2. Copy `.env.example` to `.env` and configure your settings
3. Run `npm run create-tables` to set up database tables
4. Run `./start.sh` to start the service

## Configuration

Edit the `.env` file with your Supabase credentials and other settings:

```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_service_role_key
DB_HOST=db.your-project-id.supabase.co
DB_PASSWORD=your_database_password
```

## Available Commands

- `npm start` - Start the monitor service
- `npm run start-notifications` - Start notification listener
- `npm run check-env` - Check environment configuration
- `npm run create-tables` - Create database tables
- `npm run status` - Check service status
- `npm run manual-check --id=<monitor_id>` - Manual check a monitor

## Service Management (Windows)

- `npm run install-win-service` - Install as Windows service
- `npm run uninstall-win-service` - Uninstall Windows service

## Logs

Logs are stored in the `logs/` directory:
- `monitor-service.log` - Main service logs
- `error.log` - Error logs only

## Support

For issues and documentation, visit: https://github.com/RayZwankhuizen/VUM-Backend
