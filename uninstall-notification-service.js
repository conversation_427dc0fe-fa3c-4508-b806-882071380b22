// Script to uninstall the notification listener Windows service
const Service = require('node-windows').Service;
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'VUM Notification Listener',
  description: 'Vurbis Uptime Monitor Notification Listener Service',
  script: path.join(__dirname, 'notification-listener.js')
});

// Listen for the "uninstall" event
svc.on('uninstall', function() {
  console.log('Service uninstalled successfully!');
});

// Listen for the "error" event
svc.on('error', function(err) {
  console.error('Error uninstalling service:', err);
});

// Uninstall the service
console.log('Uninstalling VUM Notification Listener service...');
svc.uninstall();
