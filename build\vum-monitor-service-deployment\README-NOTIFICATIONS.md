# VUM Email Notification System

This component of the Vurbis Uptime Monitor (VUM) system handles sending email notifications to company admins when a monitor's status changes.

## Architecture

The notification system consists of several components:

1. **Database Trigger**: A PostgreSQL trigger that fires when a monitor's status changes
2. **Notification Listener**: A Node.js service that listens for database notifications
3. **Edge Function**: A Supabase Edge Function that sends emails to company admins

## Setup Instructions

### 1. Deploy the Edge Function

First, deploy the Edge Function to your Supabase project:

```bash
cd supabase/functions
npx supabase functions deploy send-monitor-notification --project-ref axcfqil<PERSON>ombkb<PERSON>beym
```

### 2. Set Environment Variables

In the Supabase dashboard, set the following environment variables for the Edge Function:

- `RESEND_API_KEY`: Your API key for the Resend email service

### 3. Apply Database Migrations

Run the SQL migration script to create the necessary database functions and triggers:

```bash
npx supabase db push
```

Or manually run the SQL in `supabase/migrations/20240101000000_add_monitor_notification_functions.sql` in the Supabase SQL Editor.

### 4. Configure the Notification Listener

Copy the `.env.example` file to `.env` and update the values:

```bash
cp .env.example .env
```

Update the following values in the `.env` file:
- `SUPABASE_KEY`: Your Supabase anon key
- `DB_PASSWORD`: Your Supabase database password

### 5. Install Dependencies

Install the required Node.js dependencies:

```bash
npm install
```

### 6. Run the Notification Listener

Start the notification listener service:

```bash
npm run start-notifications
```

### 7. Run the Notification Listener

Start the notification listener service:

```bash
npm run start-notifications
```

For development with auto-restart:

```bash
npm run dev-notifications
```

## How It Works

1. When a monitor's status changes, the database trigger `monitor_status_change_trigger` fires
2. The trigger function `notify_monitor_status_change()` creates a notification record and sends a PostgreSQL notification
3. The notification listener service receives the notification and calls the Edge Function
4. The Edge Function retrieves the company admin emails and sends them an email notification

## Troubleshooting

Check the logs in the `logs` directory for any errors:

- `notification-listener.log`: Logs from the notification listener service

If emails are not being sent, check:
1. The Edge Function logs in the Supabase dashboard
2. That the RESEND_API_KEY is correctly set
3. That there are admin users in the company
4. That the database connection details are correct
