// <PERSON>ript to check the monitor status in the database
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkMonitorStatus() {
  try {
    console.log('Checking monitor status in the database...');

    // Get all monitors
    const { data: monitors, error: monitorsError } = await supabase
      .from('monitors')
      .select('id, name, type, target');

    if (monitorsError) {
      console.error('Error fetching monitors:', monitorsError.message);
      return;
    }

    console.log(`Found ${monitors.length} monitors:`);
    monitors.forEach(monitor => {
      console.log(`- ${monitor.name} (${monitor.type}): ${monitor.target}`);
    });

    // Get the most recent check for each monitor
    console.log('\nMost recent check for each monitor:');
    for (const monitor of monitors) {
      const { data: history, error: historyError } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitor.id)
        .order('timestamp', { ascending: false })
        .limit(1);

      if (historyError) {
        console.error(`Error fetching history for monitor ${monitor.name}:`, historyError.message);
        continue;
      }

      if (history && history.length > 0) {
        const check = history[0];
        console.log(`- ${monitor.name}: ${check.status ? 'UP' : 'DOWN'} (${check.response_time}ms) at ${check.timestamp}`);
        if (check.error_message) {
          console.log(`  Error: ${check.error_message}`);
        }
      } else {
        console.log(`- ${monitor.name}: No check history found`);
      }
    }

    // Check if the required columns exist in the monitors table
    console.log('\nChecking if required columns exist in monitors table...');

    // Check status column
    try {
      const { error: statusError } = await supabase
        .from('monitors')
        .select('status')
        .limit(1);

      if (statusError && statusError.message.includes('column "status" does not exist')) {
        console.log('Status column does not exist in monitors table. Please run the following SQL:');
        console.log(`
          ALTER TABLE monitors ADD COLUMN IF NOT EXISTS status text;
        `);
      } else {
        console.log('Status column already exists in monitors table');
      }
    } catch (error) {
      console.error('Error checking status column:', error.message);
    }

    // Check last_check_time column
    try {
      const { error: checkTimeError } = await supabase
        .from('monitors')
        .select('last_check_time')
        .limit(1);

      if (checkTimeError && checkTimeError.message.includes('column "last_check_time" does not exist')) {
        console.log('last_check_time column does not exist in monitors table. Please run the following SQL:');
        console.log(`
          ALTER TABLE monitors ADD COLUMN IF NOT EXISTS last_check_time timestamptz;
        `);
      } else {
        console.log('last_check_time column already exists in monitors table');
      }
    } catch (error) {
      console.error('Error checking last_check_time column:', error.message);
    }

    // Check last_response_time column
    try {
      const { error: responseTimeError } = await supabase
        .from('monitors')
        .select('last_response_time')
        .limit(1);

      if (responseTimeError && responseTimeError.message.includes('column "last_response_time" does not exist')) {
        console.log('last_response_time column does not exist in monitors table. Please run the following SQL:');
        console.log(`
          ALTER TABLE monitors ADD COLUMN IF NOT EXISTS last_response_time integer;
        `);
      } else {
        console.log('last_response_time column already exists in monitors table');
      }
    } catch (error) {
      console.error('Error checking last_response_time column:', error.message);
    }

    // Update the last_check_time and last_response_time for each monitor based on the most recent check
    console.log('\nUpdating monitor check times in the database...');
    for (const monitor of monitors) {
      // Get the most recent check
      const { data: history, error: historyError } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitor.id)
        .order('timestamp', { ascending: false })
        .limit(1);

      if (historyError) {
        console.error(`Error fetching history for monitor ${monitor.name}:`, historyError.message);
        continue;
      }

      if (history && history.length > 0) {
        const check = history[0];

        // Update the monitor check times
        const { error: updateError } = await supabase
          .from('monitors')
          .update({
            last_check_time: check.timestamp,
            last_response_time: check.response_time
          })
          .eq('id', monitor.id);

        if (updateError) {
          console.error(`Error updating check times for monitor ${monitor.name}:`, updateError.message);
        } else {
          console.log(`Updated check times for ${monitor.name}`);
        }
      }
    }

  } catch (error) {
    console.error('Error checking monitor status:', error.message);
  }
}

// Run the script
checkMonitorStatus().catch(console.error);
