// Test script to verify Supabase connection
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

console.log('Testing Supabase connection...');

async function testConnection() {
  try {
    // Test Supabase connection by fetching monitors
    const { data, error, count } = await supabase
      .from('monitors')
      .select('*', { count: 'exact' });

    if (error) {
      throw error;
    }

    console.log('Connection successful!');
    console.log(`Found ${data.length} monitors in the database.`);

    // Display monitor details
    console.log('\nMonitor details:');
    data.forEach(monitor => {
      console.log(`- ${monitor.name} (${monitor.id})`);
      console.log(`  Type: ${monitor.type}`);
      console.log(`  Target: ${monitor.target}`);
      console.log(`  Interval: ${monitor.interval} minutes`);
      console.log(`  Active: ${monitor.active ? 'Yes' : 'No'}`);
      console.log('');
    });

    console.log('Test completed successfully.');
  } catch (error) {
    console.error('Error connecting to Supabase:', error.message);
    console.error('Please check your Supabase URL and API key.');
  }
}

testConnection();
