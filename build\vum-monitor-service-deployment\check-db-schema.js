// Script to check the database schema
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkSchema() {
  try {
    console.log('Checking database schema...');

    // Check monitor_history table
    console.log('\nChecking monitor_history table:');
    const { data: historyColumns, error: historyError } = await supabase
      .rpc('get_table_columns', { table_name: 'monitor_history' });

    if (historyError) {
      console.error('Error fetching monitor_history columns:', historyError.message);
    } else {
      console.log('monitor_history columns:');
      historyColumns.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
      });
    }

    // Check monitors table
    console.log('\nChecking monitors table:');
    const { data: monitorColumns, error: monitorError } = await supabase
      .rpc('get_table_columns', { table_name: 'monitors' });

    if (monitorError) {
      console.error('Error fetching monitors columns:', monitorError.message);
    } else {
      console.log('monitors columns:');
      monitorColumns.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
      });
    }

    // Check if the RPC function exists
    console.log('\nChecking if insert_monitor_history RPC function exists:');
    const { data: functions, error: functionError } = await supabase
      .rpc('get_functions');

    if (functionError) {
      console.error('Error fetching functions:', functionError.message);
    } else {
      const insertFunction = functions.find(f => f.name === 'insert_monitor_history');
      if (insertFunction) {
        console.log('insert_monitor_history function exists');
        console.log(`- Arguments: ${insertFunction.arguments}`);
        console.log(`- Return type: ${insertFunction.return_type}`);
      } else {
        console.log('insert_monitor_history function does not exist');
      }
    }

    // Create the get_table_columns and get_functions RPC functions if they don't exist
    console.log('\nCreating helper functions if they don\'t exist:');
    
    // Create get_table_columns function
    const { error: createColumnsError } = await supabase
      .rpc('create_get_table_columns_function');
      
    if (createColumnsError) {
      console.error('Error creating get_table_columns function:', createColumnsError.message);
    } else {
      console.log('get_table_columns function created or already exists');
    }
    
    // Create get_functions function
    const { error: createFunctionsError } = await supabase
      .rpc('create_get_functions_function');
      
    if (createFunctionsError) {
      console.error('Error creating get_functions function:', createFunctionsError.message);
    } else {
      console.log('get_functions function created or already exists');
    }

  } catch (error) {
    console.error('Error checking schema:', error.message);
  }
}

// Create the helper functions in the database
async function createHelperFunctions() {
  try {
    console.log('Creating helper functions in the database...');

    // Create get_table_columns function
    const { error: columnsError } = await supabase
      .from('_functions')
      .upsert({
        name: 'get_table_columns',
        definition: `
          CREATE OR REPLACE FUNCTION get_table_columns(table_name text)
          RETURNS TABLE(column_name text, data_type text, is_nullable text)
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          BEGIN
            RETURN QUERY
            SELECT c.column_name::text, c.data_type::text, c.is_nullable::text
            FROM information_schema.columns c
            WHERE c.table_name = table_name
            ORDER BY c.ordinal_position;
          END;
          $$;
        `
      });

    if (columnsError) {
      console.error('Error creating get_table_columns function:', columnsError.message);
    } else {
      console.log('get_table_columns function created');
    }

    // Create get_functions function
    const { error: functionsError } = await supabase
      .from('_functions')
      .upsert({
        name: 'get_functions',
        definition: `
          CREATE OR REPLACE FUNCTION get_functions()
          RETURNS TABLE(name text, arguments text, return_type text)
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          BEGIN
            RETURN QUERY
            SELECT p.proname::text, pg_get_function_arguments(p.oid)::text, pg_get_function_result(p.oid)::text
            FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'public'
            ORDER BY p.proname;
          END;
          $$;
        `
      });

    if (functionsError) {
      console.error('Error creating get_functions function:', functionsError.message);
    } else {
      console.log('get_functions function created');
    }

  } catch (error) {
    console.error('Error creating helper functions:', error.message);
  }
}

// Run the script
async function run() {
  await createHelperFunctions();
  await checkSchema();
}

run().catch(console.error);
