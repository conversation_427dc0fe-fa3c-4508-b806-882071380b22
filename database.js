const { createClient } = require('@supabase/supabase-js');
const logger = require('./logger');

// Load environment variables
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Table names
const MONITOR_TABLE = process.env.MONITOR_TABLE || 'monitors';
const HISTORY_TABLE = process.env.HISTORY_TABLE || 'monitor_history';
const NOTIFICATION_TABLE = process.env.NOTIFICATION_TABLE || 'notifications';

// Get all monitors
async function getAllMonitors() {
  try {
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('*');

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error(`Error fetching monitors: ${error.message}`);
    return [];
  }
}

// Get monitor by ID
async function getMonitorById(id) {
  try {
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('*')
      .eq('id', id)
      .limit(1)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error fetching monitor ${id}: ${error.message}`);
    return null;
  }
}

// Get last check for a monitor
async function getLastCheck(monitorId) {
  try {
    const { data, error } = await supabase
      .from(HISTORY_TABLE)
      .select('*')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows returned"
      throw error;
    }

    return data || null;
  } catch (error) {
    logger.error(`Error fetching last check for monitor ${monitorId}: ${error.message}`);
    return null;
  }
}

// Save check result
async function saveCheckResult(monitorId, status, responseTime, errorMessage) {
  try {
    // Handle status values
    let statusToSave = status;
    let statusDisplay = status;

    // Ensure status is a string
    if (typeof status === 'string') {
      // Handle string values that might be "true" or "false" (as strings)
      if (status.toLowerCase() === 'true') {
        statusDisplay = 'UP';
        statusToSave = 'up';
      } else if (status.toLowerCase() === 'false') {
        statusDisplay = 'DOWN';
        statusToSave = 'down';
      } else {
        statusDisplay = status.toUpperCase();
        statusToSave = status.toLowerCase();
      }

      // For degraded status, add it to the error message
      if (statusToSave === 'degraded') {
        if (!errorMessage) {
          errorMessage = `Slow response time: ${responseTime}ms (threshold: 1000ms)`;
        }
      }
    }
    // If status is a boolean, convert to string for both display and storage
    else if (typeof status === 'boolean') {
      statusDisplay = status ? 'UP' : 'DOWN';
      statusToSave = status ? 'up' : 'down';
    }

    logger.info(`Saving check result for monitor ${monitorId} with status: ${statusDisplay} (${typeof status})`);

    const checkResult = {
      monitor_id: monitorId,
      status: statusToSave,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };

    const { error } = await supabase
      .from(HISTORY_TABLE)
      .insert(checkResult);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    logger.error(`Error saving check result for monitor ${monitorId}: ${error.message}`);
    return false;
  }
}

// Create notification and send email
async function createNotification(monitor, status) {
  try {
    // Handle both string and boolean status values
    let notificationType = '';
    let statusDisplay = '';

    // Convert status to appropriate notification type and display text
    if (typeof status === 'string') {
      if (status === 'up') {
        notificationType = 'up';
        statusDisplay = 'UP';
      } else if (status === 'down') {
        notificationType = 'down';
        statusDisplay = 'DOWN';
      } else if (status === 'degraded') {
        notificationType = 'degraded';
        statusDisplay = 'DEGRADED';
      } else {
        notificationType = 'other';
        statusDisplay = status.toUpperCase();
      }
    } else if (typeof status === 'boolean') {
      notificationType = status ? 'up' : 'down';
      statusDisplay = status ? 'UP' : 'DOWN';
    }

    logger.info(`Creating notification for monitor ${monitor.name} with status: ${statusDisplay}`);

    // Get all companies associated with this monitor
    const { data: monitorCompanies, error: companiesError } = await supabase
      .from('monitor_companies')
      .select('company_id')
      .eq('monitor_id', monitor.id);

    if (companiesError) {
      throw companiesError;
    }

    // If no companies are associated, use the monitor's company_id
    if (!monitorCompanies || monitorCompanies.length === 0) {
      if (monitor.company_id) {
        // Create notification for the monitor's company
        const notification = {
          monitor_id: monitor.id,
          company_id: monitor.company_id,
          message: `Monitor ${monitor.name} is now ${statusDisplay}`,
          type: notificationType,
          read: false
        };

        const { error } = await supabase
          .from(NOTIFICATION_TABLE)
          .insert(notification);

        if (error) {
          throw error;
        }

        // Send email notification
        await sendEmailNotification(monitor, notificationType, monitor.company_id);
      }
    } else {
      // Create notifications for each company
      for (const mc of monitorCompanies) {
        const notification = {
          monitor_id: monitor.id,
          company_id: mc.company_id,
          message: `Monitor ${monitor.name} is now ${statusDisplay}`,
          type: notificationType,
          read: false
        };

        const { error } = await supabase
          .from(NOTIFICATION_TABLE)
          .insert(notification);

        if (error) {
          logger.error(`Error creating notification for company ${mc.company_id}: ${error.message}`);
          continue;
        }

        // Send email notification
        await sendEmailNotification(monitor, notificationType, mc.company_id);
      }
    }

    return true;
  } catch (error) {
    logger.error(`Error creating notification for monitor ${monitor.id}: ${error.message}`);
    return false;
  }
}

// This function has been removed as user_id is no longer used in notifications

// Send email notification to company admins
async function sendEmailNotification(monitor, status, companyId) {
  try {
    // Check if email alerts are enabled
    const enableEmailAlerts = process.env.ENABLE_EMAIL_ALERTS === 'true';

    if (!enableEmailAlerts) {
      logger.info(`Email alerts are disabled. Skipping email notification for monitor ${monitor.name} (${status}) to company ${companyId}`);
      return true; // Return true to indicate "success" (as in, we did what was configured)
    }

    logger.info(`Sending email notification for monitor ${monitor.name} (${status}) to company ${companyId}`);

    // Get company details
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('name')
      .eq('id', companyId)
      .single();

    if (companyError) {
      throw new Error(`Failed to get company details: ${companyError.message}`);
    }

    // Get company admin emails directly from the database
    const { data: adminData, error: adminEmailsError } = await supabase
      .from('company_members')
      .select('user_id')
      .eq('company_id', companyId)
      .eq('role_type', 'admin');

    if (adminEmailsError) {
      throw new Error(`Failed to get admin emails: ${adminEmailsError.message}`);
    }

    // Get the email addresses for these users
    const userIds = adminData.map(item => item.user_id);

    if (userIds.length === 0) {
      logger.warn(`No admin users found for company ${companyId}`);
      return false;
    }

    // Get emails from auth.users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .in('id', userIds);

    if (userError) {
      throw new Error(`Failed to get user emails: ${userError.message}`);
    }

    // Extract email addresses
    const adminEmails = userData.map(user => user.email).filter(Boolean);

    // Get all superadmin emails
    const { data: superadminEmails, error: superadminError } = await supabase
      .rpc('get_all_superadmin_emails');

    if (superadminError) {
      logger.warn(`Error getting superadmin emails: ${superadminError.message}`);
    } else if (superadminEmails && superadminEmails.length > 0) {
      // Add superadmin emails to the recipients list
      logger.info(`Found ${superadminEmails.length} superadmin emails to notify`);
      adminEmails.push(...superadminEmails);
    }

    if (!adminEmails || adminEmails.length === 0) {
      logger.warn(`No admin or superadmin emails found for company ${companyId}`);
      return false;
    }

    // Remove duplicates
    const uniqueEmails = [...new Set(adminEmails)];

    logger.info(`Found ${uniqueEmails.length} unique emails for notifications (company admins + superadmins)`);

    // Create email content
    const statusText = status === 'up' ? 'UP' : status === 'down' ? 'DOWN' : 'DEGRADED';
    const statusColor = status === 'up' ? '#4CAF50' : status === 'down' ? '#F44336' : '#FF9800';

    const subject = `[${company.name}] Monitor ${monitor.name} is ${statusText}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Monitor Status Change</h2>
        <div style="padding: 20px; border-radius: 5px; margin-bottom: 20px; background-color: ${statusColor}; color: white;">
          <h3 style="margin-top: 0;">Monitor is now ${statusText}</h3>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>Company:</strong> ${company.name}</p>
          <p><strong>Monitor:</strong> ${monitor.name}</p>
          <p><strong>Type:</strong> ${monitor.type}</p>
          <p><strong>Target:</strong> ${monitor.target}</p>
          <p><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 30px;">
          <p>This is an automated message from Vurbis Uptime Monitor.</p>
        </div>
      </div>
    `;

    // Send email using Resend API
    const RESEND_API_KEY = process.env.RESEND_API_KEY;

    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not set in .env file');
    }

    logger.info(`Sending email to ${uniqueEmails.join(', ')}`);

    const axios = require('axios');
    const response = await axios.post('https://api.resend.com/emails', {
      from: 'Vurbis Uptime Monitor <<EMAIL>>',
      to: uniqueEmails,
      subject,
      html,
    }, {
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200) {
      logger.info(`Email notification sent successfully to ${uniqueEmails.length} recipients (company admins + superadmins)`);
      return true;
    } else {
      throw new Error(`Failed to send email notification: ${response.statusText}`);
    }
  } catch (error) {
    logger.error(`Error sending email notification: ${error.message}`);
    return false;
  }
}

// Test database connection
async function testConnection() {
  try {
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('id, name')
      .limit(1);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    logger.error(`Database connection test failed: ${error.message}`);
    return false;
  }
}

module.exports = {
  supabase,
  getAllMonitors,
  getMonitorById,
  getLastCheck,
  saveCheckResult,
  createNotification,
  sendEmailNotification,
  testConnection,
  MONITOR_TABLE,
  HISTORY_TABLE,
  NOTIFICATION_TABLE
};
