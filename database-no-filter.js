const { createClient } = require('@supabase/supabase-js');
const logger = require('./logger');

// Load environment variables
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Table names
const MONITOR_TABLE = process.env.MONITOR_TABLE || 'monitors';
const HISTORY_TABLE = process.env.HISTORY_TABLE || 'monitor_history';
const NOTIFICATION_TABLE = process.env.NOTIFICATION_TABLE || 'notifications';

// Get all monitors (without filtering by active status)
async function getAllMonitors() {
  try {
    logger.info(`Fetching all monitors from table '${MONITOR_TABLE}' without filtering by active status`);
    
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('*');
      
    if (error) {
      throw error;
    }
    
    if (!data || data.length === 0) {
      logger.warn(`No monitors found in table '${MONITOR_TABLE}'`);
      return [];
    }
    
    logger.info(`Found ${data.length} total monitors in table '${MONITOR_TABLE}'`);
    
    // Log the first monitor as a sample
    if (data.length > 0) {
      logger.info(`Sample monitor: ${JSON.stringify(data[0])}`);
    }
    
    return data;
  } catch (error) {
    logger.error(`Error fetching monitors: ${error.message}`);
    return [];
  }
}

// Get monitor by ID
async function getMonitorById(id) {
  try {
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('*')
      .eq('id', id)
      .limit(1)
      .single();
      
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    logger.error(`Error fetching monitor ${id}: ${error.message}`);
    return null;
  }
}

// Get last check for a monitor
async function getLastCheck(monitorId) {
  try {
    const { data, error } = await supabase
      .from(HISTORY_TABLE)
      .select('*')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();
      
    if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows returned"
      throw error;
    }
    
    return data || null;
  } catch (error) {
    logger.error(`Error fetching last check for monitor ${monitorId}: ${error.message}`);
    return null;
  }
}

// Save check result
async function saveCheckResult(monitorId, status, responseTime, errorMessage) {
  try {
    const checkResult = {
      monitor_id: monitorId,
      status,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };
    
    logger.debug(`Saving check result for monitor ${monitorId}: ${JSON.stringify(checkResult)}`);
    
    const { error } = await supabase
      .from(HISTORY_TABLE)
      .insert(checkResult);
      
    if (error) {
      throw error;
    }
    
    logger.info(`Successfully saved check result for monitor ${monitorId}`);
    return true;
  } catch (error) {
    logger.error(`Error saving check result for monitor ${monitorId}: ${error.message}`);
    return false;
  }
}

// Create notification
async function createNotification(monitor, status) {
  try {
    const notification = {
      monitor_id: monitor.id,
      user_id: monitor.user_id,
      company_id: monitor.company_id,
      message: `Monitor ${monitor.name} is now ${status ? 'UP' : 'DOWN'}`,
      type: status ? 'up' : 'down',
      read: false
    };
    
    logger.debug(`Creating notification for monitor ${monitor.id}: ${JSON.stringify(notification)}`);
    
    const { error } = await supabase
      .from(NOTIFICATION_TABLE)
      .insert(notification);
      
    if (error) {
      throw error;
    }
    
    logger.info(`Successfully created notification for monitor ${monitor.id}`);
    return true;
  } catch (error) {
    logger.error(`Error creating notification for monitor ${monitor.id}: ${error.message}`);
    return false;
  }
}

// Test database connection
async function testConnection() {
  try {
    logger.info('Testing database connection...');
    
    // Try to get a list of tables
    const { data: tables, error: tablesError } = await supabase.rpc('get_tables');
    
    if (tablesError) {
      logger.warn(`Could not get tables list: ${tablesError.message}`);
      
      // Try to access the monitors table directly
      const { data, error } = await supabase
        .from(MONITOR_TABLE)
        .select('id, name')
        .limit(1);
        
      if (error) {
        throw error;
      }
      
      logger.info(`Successfully connected to database and accessed '${MONITOR_TABLE}' table`);
    } else {
      logger.info(`Successfully connected to database. Found ${tables.length} tables.`);
      
      // Check if our tables exist
      const tableNames = tables.map(t => t.table_name);
      logger.info(`Tables: ${tableNames.join(', ')}`);
      
      if (!tableNames.includes(MONITOR_TABLE)) {
        logger.warn(`Monitor table '${MONITOR_TABLE}' not found in database!`);
      }
      
      if (!tableNames.includes(HISTORY_TABLE)) {
        logger.warn(`History table '${HISTORY_TABLE}' not found in database!`);
      }
      
      if (!tableNames.includes(NOTIFICATION_TABLE)) {
        logger.warn(`Notification table '${NOTIFICATION_TABLE}' not found in database!`);
      }
    }
    
    return true;
  } catch (error) {
    logger.error(`Database connection test failed: ${error.message}`);
    return false;
  }
}

// List all tables
async function listTables() {
  try {
    logger.info('Listing all tables...');
    
    // Try to get a list of tables
    const { data: tables, error: tablesError } = await supabase.rpc('get_tables');
    
    if (tablesError) {
      logger.warn(`Could not get tables list using RPC: ${tablesError.message}`);
      
      // Try direct query to information_schema
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
        
      if (schemaError) {
        logger.error(`Could not get tables from information_schema: ${schemaError.message}`);
        return [];
      }
      
      return schemaData.map(t => t.table_name);
    }
    
    return tables.map(t => t.table_name);
  } catch (error) {
    logger.error(`Error listing tables: ${error.message}`);
    return [];
  }
}

module.exports = {
  supabase,
  getAllMonitors,
  getMonitorById,
  getLastCheck,
  saveCheckResult,
  createNotification,
  testConnection,
  listTables,
  MONITOR_TABLE,
  HISTORY_TABLE,
  NOTIFICATION_TABLE
};
