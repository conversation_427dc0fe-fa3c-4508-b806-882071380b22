const axios = require('axios');
const logger = require('./logger');
const db = require('./database');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Check a single monitor
async function checkMonitor(monitor, isManualCheck = false) {
  if (isManualCheck) {
    logger.info(`*** MANUAL CHECK REQUESTED: Checking monitor "${monitor.name}" (ID: ${monitor.id}) ***`);
  }

  logger.info(`Checking monitor: ${monitor.name} (${monitor.id})`);
  logger.info(`Target: ${monitor.target}, Type: ${monitor.type}, Timeout: ${monitor.timeout || 30}s`);

  const startTime = Date.now();
  let status = false;
  let responseTime = null;
  let errorMessage = null;

  try {
    switch (monitor.type) {
      case 'http':
        // HTTP check
        try {
          logger.info(`Sending HTTP request to ${monitor.target}...`);

          // Add more detailed request options
          const requestOptions = {
            timeout: (monitor.timeout || 30) * 1000, // Default 30 seconds
            validateStatus: null, // Don't throw on non-2xx responses
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)'
            },
            // Add this to see the full error
            maxRedirects: 5 // Allow up to 5 redirects
          };

          logger.info(`Request options: ${JSON.stringify(requestOptions)}`);

          const response = await axios.get(monitor.target, requestOptions);

          status = response.status >= 200 && response.status < 300;
          responseTime = Date.now() - startTime;

          logger.info(`Response received: Status ${response.status}, Time ${responseTime}ms`);

          if (!status) {
            errorMessage = `HTTP status: ${response.status}`;
            logger.warn(`Non-success status code: ${response.status}`);
          }
        } catch (error) {
          logger.error(`HTTP request error: ${error.message}`);
          if (error.response) {
            logger.error(`Response status: ${error.response.status}`);
          }
          if (error.request) {
            logger.error('Request was made but no response was received');
          }
          throw error;
        }
        break;

      case 'ping':
        // Ping check (implemented as a simple HTTP HEAD request)
        try {
          logger.info(`Sending ping request to ${monitor.target}...`);

          // Use a HEAD request for faster response
          const pingOptions = {
            method: 'HEAD',
            timeout: (monitor.timeout || 30) * 1000,
            validateStatus: null, // Don't throw on non-2xx responses
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)',
              'Cache-Control': 'no-cache'
            },
            maxRedirects: 5
          };

          logger.info(`Ping options: ${JSON.stringify(pingOptions)}`);

          try {
            const pingResponse = await axios(monitor.target, pingOptions);

            // For ping, we consider any response as successful
            status = true;
            responseTime = Date.now() - startTime;

            logger.info(`Ping response received: Status ${pingResponse.status}, Time ${responseTime}ms`);
          } catch (error) {
            // For ping, we'll try a GET request as a fallback
            logger.info(`HEAD request failed, trying GET request as fallback...`);

            const getOptions = {
              method: 'GET',
              timeout: (monitor.timeout || 30) * 1000,
              validateStatus: null,
              headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)',
                'Cache-Control': 'no-cache'
              },
              maxRedirects: 5
            };

            try {
              const getResponse = await axios(monitor.target, getOptions);

              // For ping, we consider any response as successful
              status = true;
              responseTime = Date.now() - startTime;

              logger.info(`GET response received: Status ${getResponse.status}, Time ${responseTime}ms`);
            } catch (innerError) {
              // If both HEAD and GET fail, the host is down
              status = false;
              responseTime = Date.now() - startTime;
              throw new Error(`Ping failed: ${innerError.message}`);
            }
          }
        } catch (error) {
          logger.error(`Ping request error: ${error.message}`);
          status = false;
          throw error;
        }
        break;

      case 'port':
        // Port check
        try {
          logger.info(`Checking port(s) for ${monitor.target}...`);

          // Parse the target (format: host|port1,port2,port3)
          const [host, portsStr] = monitor.target.split('|');
          const ports = portsStr.split(',').map(p => parseInt(p.trim()));

          logger.info(`Host: ${host}, Ports: ${ports.join(', ')}`);

          // Check if at least one port is open
          let anyPortOpen = false;
          let portResults = [];
          let totalResponseTime = 0;

          // First, check if the host exists using DNS resolution
          try {
            const { lookup } = require('dns');
            const util = require('util');
            const lookupPromise = util.promisify(lookup);

            const hostIp = await lookupPromise(host);
            logger.info(`Host ${host} resolved successfully to ${JSON.stringify(hostIp)}.`);

            // Now check each port
            for (const port of ports) {
              const portStartTime = Date.now();
              let portStatus = false;
              let portError = null;

              try {
                // For HTTP/HTTPS ports, we can use axios
                if (port === 80 || port === 443 || port === 8080 || port === 8443) {
                  const protocol = (port === 443 || port === 8443) ? 'https' : 'http';
                  const url = `${protocol}://${host}:${port}`;

                  try {
                    const portResponse = await axios({
                      method: 'HEAD',
                      url,
                      timeout: 5000,
                      validateStatus: null,
                      headers: { 'Cache-Control': 'no-cache' }
                    });

                    // Any response means the port is open
                    portStatus = true;
                    logger.info(`Port ${port} is open (HTTP/HTTPS).`);
                  } catch (fetchError) {
                    // Some errors actually indicate the port is open
                    if (fetchError.code === 'ECONNRESET' ||
                        fetchError.code === 'ETIMEDOUT' ||
                        fetchError.message.includes('certificate') ||
                        fetchError.message.includes('SSL')) {
                      portStatus = true;
                      logger.info(`Port ${port} is likely open (connection reset/timeout/SSL error).`);
                    } else {
                      portError = fetchError.message;
                      logger.info(`Port ${port} check failed: ${portError}`);
                    }
                  }
                } else {
                  // For non-HTTP ports, we'll use a simple TCP connection check
                  const net = require('net');

                  // Create a promise-based version of the TCP connection check
                  const checkTcpPort = () => {
                    return new Promise((resolve, reject) => {
                      const socket = new net.Socket();

                      // Set a timeout for the connection attempt
                      socket.setTimeout(5000);

                      // Handle successful connection
                      socket.on('connect', () => {
                        socket.end();
                        resolve(true);
                      });

                      // Handle connection errors
                      socket.on('error', (err) => {
                        socket.destroy();
                        reject(err);
                      });

                      // Handle connection timeout
                      socket.on('timeout', () => {
                        socket.destroy();
                        reject(new Error('Connection timeout'));
                      });

                      // Attempt to connect
                      socket.connect(port, host);
                    });
                  };

                  try {
                    await checkTcpPort();
                    portStatus = true;
                    logger.info(`Port ${port} is open (TCP).`);
                  } catch (tcpError) {
                    portError = tcpError.message;
                    logger.info(`Port ${port} is closed or filtered: ${portError}`);
                  }
                }
              } catch (portCheckError) {
                portError = portCheckError.message;
                logger.error(`Error checking port ${port}: ${portError}`);
              }

              const portResponseTime = Date.now() - portStartTime;
              totalResponseTime += portResponseTime;

              if (portStatus) {
                anyPortOpen = true;
              }

              portResults.push({
                port,
                status: portStatus,
                responseTime: portResponseTime,
                error: portError
              });
            }

            // Overall status is true if any port is open
            status = anyPortOpen;
            responseTime = ports.length > 0 ? totalResponseTime / ports.length : 0;

            if (!status) {
              const failedPorts = portResults
                .filter(r => !r.status)
                .map(r => r.port)
                .join(', ');
              errorMessage = `All ports failed (${failedPorts})`;
            }
          } catch (dnsError) {
            logger.error(`DNS resolution failed for ${host}: ${dnsError.message}`);
            status = false;
            errorMessage = `Host not found: ${dnsError.message}`;
          }
        } catch (error) {
          logger.error(`Port check error: ${error.message}`);
          status = false;
          errorMessage = `Port check failed: ${error.message}`;
        }
        break;

      default:
        // For any other monitor type, just mark it as up for now
        logger.warn(`Monitor type '${monitor.type}' is not fully implemented yet, marking as UP`);
        status = true;
        responseTime = 0;
    }
  } catch (error) {
    status = false;
    responseTime = Date.now() - startTime;
    errorMessage = `Error: ${error.message}`;
    logger.error(`Check failed for monitor ${monitor.name}: ${error.message}`);
  }

  // Convert boolean status to string status if needed
  let stringStatus = status;
  if (typeof status === 'boolean') {
    // Check if this is a degraded status based on response time
    if (status === true && responseTime > 1000) {
      stringStatus = 'degraded';
      if (!errorMessage) {
        errorMessage = `Slow response time: ${responseTime}ms (threshold: 1000ms)`;
      }
    } else {
      stringStatus = status ? 'up' : 'down';
    }
  }

  // Update the monitor's status in the database
  try {
    const { error } = await supabase
      .from('monitors')
      .update({
        status: stringStatus,
        last_check_time: new Date().toISOString(),
        last_response_time: responseTime
      })
      .eq('id', monitor.id);

    if (error) {
      logger.error(`Error updating monitor status: ${error.message}`);
    } else {
      logger.info(`Updated monitor status to ${stringStatus.toUpperCase()}`);
    }
  } catch (error) {
    logger.error(`Failed to update monitor status: ${error.message}`);
  }

  // Save the check result
  const saved = await db.saveCheckResult(monitor.id, stringStatus, responseTime, errorMessage);

  if (saved) {
    logger.info(`Saved check result for ${monitor.name}: ${stringStatus.toUpperCase()} (${responseTime}ms)`);

    // Check if status changed and send notification if needed
    await checkStatusChange(monitor, stringStatus);
  }

  return {
    monitor_id: monitor.id,
    name: monitor.name,
    status,
    response_time: responseTime,
    error_message: errorMessage
  };
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(monitor, currentStatus) {
  try {
    // Handle both string and boolean status values
    let statusForComparison = currentStatus;
    let statusForDisplay = currentStatus;

    // Convert boolean to string if needed
    if (typeof currentStatus === 'boolean') {
      statusForComparison = currentStatus ? 'up' : 'down';
      statusForDisplay = currentStatus ? 'UP' : 'DOWN';
    } else if (typeof currentStatus === 'string') {
      statusForDisplay = currentStatus.toUpperCase();
    }

    logger.info(`Checking status change for monitor ${monitor.name} with current status: ${statusForDisplay}`);

    // Get the previous check
    const lastCheck = await db.getLastCheck(monitor.id);
    const previousCheck = await getPreviousCheck(monitor.id, lastCheck);

    // If this is the first check or status changed, send notification
    if (!previousCheck) {
      logger.info(`First check for monitor ${monitor.name}: ${statusForDisplay}`);
      await db.createNotification(monitor, statusForComparison);
      return true;
    }

    // Compare previous status with current status
    let previousStatus = previousCheck.status;

    // Convert previous boolean status to string if needed
    if (typeof previousStatus === 'boolean') {
      previousStatus = previousStatus ? 'up' : 'down';
    }

    if (previousStatus !== statusForComparison) {
      logger.info(`Status changed for monitor ${monitor.name}: ${previousStatus.toUpperCase()} -> ${statusForDisplay}`);
      await db.createNotification(monitor, statusForComparison);
      return true;
    }

    return false;
  } catch (error) {
    logger.error(`Failed to check status change: ${error.message}`);
    return false;
  }
}

// Get the previous check (second most recent)
async function getPreviousCheck(monitorId, lastCheck) {
  if (!lastCheck) return null;

  try {
    const { data, error } = await db.supabase
      .from(db.HISTORY_TABLE)
      .select('*')
      .eq('monitor_id', monitorId)
      .lt('timestamp', lastCheck.timestamp) // Get checks before the last check
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows returned"
      throw error;
    }

    return data || null;
  } catch (error) {
    logger.error(`Error fetching previous check for monitor ${monitorId}: ${error.message}`);
    return null;
  }
}

// Check if a monitor is due for a check
async function isMonitorDueForCheck(monitor) {
  try {
    const lastCheck = await db.getLastCheck(monitor.id);

    // If no previous check, it's due
    if (!lastCheck) {
      return true;
    }

    const now = Date.now();
    const lastCheckTime = new Date(lastCheck.timestamp).getTime();
    const intervalMs = (monitor.interval || 5) * 60 * 1000; // Default 5 minutes

    // Check if it's time to check this monitor
    return now - lastCheckTime >= intervalMs;
  } catch (error) {
    logger.error(`Error checking if monitor ${monitor.id} is due: ${error.message}`);
    return true; // If there's an error, check anyway
  }
}

module.exports = {
  checkMonitor,
  isMonitorDueForCheck
};
