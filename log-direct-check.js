// <PERSON>ript to log a direct check message to the monitor-service.log file
const fs = require('fs');
const path = require('path');
const os = require('os');

// Configuration
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];
const monitorName = args.find(arg => arg.startsWith('--name='))?.split('=')[1];

// Log the direct check message
const timestamp = new Date().toISOString();
let message;

if (monitorId && monitorName) {
  message = `[${timestamp}] [INFO] [DIRECT-CHECK] *** DIRECT CHECK REQUESTED: Checking monitor "${monitorName}" (ID: ${monitorId}) ***`;
} else {
  message = `[${timestamp}] [INFO] [DIRECT-CHECK] *** DIRECT CHECK REQUESTED: Checking ALL MONITORS ***`;
}

// Log to console
console.log(message);

// Log to file
try {
  fs.appendFileSync(LOG_FILE, message + os.EOL);
  console.log('Successfully logged direct check message to monitor-service.log');
} catch (error) {
  console.error(`Error writing to log file: ${error.message}`);
}
