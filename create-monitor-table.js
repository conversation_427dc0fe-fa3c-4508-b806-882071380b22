// <PERSON><PERSON><PERSON> to create the monitors table if it doesn't exist
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const MONITOR_TABLE = process.env.MONITOR_TABLE || 'monitors';
const HISTORY_TABLE = process.env.HISTORY_TABLE || 'monitor_history';
const NOTIFICATION_TABLE = process.env.NOTIFICATION_TABLE || 'notifications';

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function createTables() {
  console.log('=== TABLE CREATION TOOL ===');
  console.log(`Supabase URL: ${SUPABASE_URL}`);
  console.log(`Supabase Key: ${SUPABASE_KEY.substring(0, 10)}...${SUPABASE_KEY.substring(SUPABASE_KEY.length - 5)}`);

  // Check if tables exist
  console.log('\nChecking if tables exist...');

  // Try to get a list of tables
  let tables = [];
  try {
    const { data: tableData, error: tableError } = await supabase.rpc('get_tables');

    if (tableError) {
      console.log(`Could not get tables list using RPC: ${tableError.message}`);

      // Try direct query to information_schema
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');

      if (schemaError) {
        console.log(`Could not get tables from information_schema: ${schemaError.message}`);
      } else {
        tables = schemaData.map(t => t.table_name);
      }
    } else {
      tables = tableData.map(t => t.table_name);
    }
  } catch (error) {
    console.log(`Error listing tables: ${error.message}`);
  }

  console.log(`Found ${tables.length} tables: ${tables.join(', ')}`);

  // Check if monitors table exists
  if (tables.includes(MONITOR_TABLE)) {
    console.log(`\nMonitors table '${MONITOR_TABLE}' already exists.`);

    // Check if it has any records
    try {
      const { data, error } = await supabase
        .from(MONITOR_TABLE)
        .select('count(*)', { count: 'exact', head: true });

      if (error) {
        console.log(`Error counting records in '${MONITOR_TABLE}': ${error.message}`);
      } else {
        console.log(`Table '${MONITOR_TABLE}' has ${data[0].count} records.`);
      }
    } catch (error) {
      console.log(`Error counting records: ${error.message}`);
    }
  } else {
    console.log(`\nMonitors table '${MONITOR_TABLE}' does not exist. Creating it...`);

    // Create the monitors table using SQL
    try {
      const { error } = await supabase.rpc('execute_sql', {
        query_text: `
          CREATE TABLE ${MONITOR_TABLE} (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            target TEXT NOT NULL,
            interval INTEGER NOT NULL DEFAULT 5,
            timeout INTEGER NOT NULL DEFAULT 30,
            active BOOLEAN NOT NULL DEFAULT true,
            user_id UUID,
            company_id UUID,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
          );
        `
      });

      if (error) {
        console.log(`Error creating '${MONITOR_TABLE}' table: ${error.message}`);
      } else {
        console.log(`Successfully created '${MONITOR_TABLE}' table!`);
      }
    } catch (error) {
      console.log(`Error creating table: ${error.message}`);
    }
  }

  // Check if monitor_history table exists
  if (tables.includes(HISTORY_TABLE)) {
    console.log(`\nHistory table '${HISTORY_TABLE}' already exists.`);

    // Check if it has any records
    try {
      const { data, error } = await supabase
        .from(HISTORY_TABLE)
        .select('count(*)', { count: 'exact', head: true });

      if (error) {
        console.log(`Error counting records in '${HISTORY_TABLE}': ${error.message}`);
      } else {
        console.log(`Table '${HISTORY_TABLE}' has ${data[0].count} records.`);
      }
    } catch (error) {
      console.log(`Error counting records: ${error.message}`);
    }
  } else {
    console.log(`\nHistory table '${HISTORY_TABLE}' does not exist. Creating it...`);

    // Create the monitor_history table using SQL
    try {
      const { error } = await supabase.rpc('execute_sql', {
        query_text: `
          CREATE TABLE ${HISTORY_TABLE} (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            monitor_id UUID NOT NULL,
            status BOOLEAN NOT NULL,
            response_time INTEGER,
            error_message TEXT,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT now()
          );
        `
      });

      if (error) {
        console.log(`Error creating '${HISTORY_TABLE}' table: ${error.message}`);
      } else {
        console.log(`Successfully created '${HISTORY_TABLE}' table!`);
      }
    } catch (error) {
      console.log(`Error creating table: ${error.message}`);
    }
  }

  // Check if notifications table exists
  if (tables.includes(NOTIFICATION_TABLE)) {
    console.log(`\nNotifications table '${NOTIFICATION_TABLE}' already exists.`);

    // Check if it has any records
    try {
      const { data, error } = await supabase
        .from(NOTIFICATION_TABLE)
        .select('count(*)', { count: 'exact', head: true });

      if (error) {
        console.log(`Error counting records in '${NOTIFICATION_TABLE}': ${error.message}`);
      } else {
        console.log(`Table '${NOTIFICATION_TABLE}' has ${data[0].count} records.`);
      }
    } catch (error) {
      console.log(`Error counting records: ${error.message}`);
    }
  } else {
    console.log(`\nNotifications table '${NOTIFICATION_TABLE}' does not exist. Creating it...`);

    // Create the notifications table using SQL
    try {
      const { error } = await supabase.rpc('execute_sql', {
        query_text: `
          CREATE TABLE ${NOTIFICATION_TABLE} (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            monitor_id UUID NOT NULL,
            user_id UUID,
            company_id UUID,
            message TEXT NOT NULL,
            type TEXT NOT NULL,
            read BOOLEAN NOT NULL DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
          );
        `
      });

      if (error) {
        console.log(`Error creating '${NOTIFICATION_TABLE}' table: ${error.message}`);
      } else {
        console.log(`Successfully created '${NOTIFICATION_TABLE}' table!`);
      }
    } catch (error) {
      console.log(`Error creating table: ${error.message}`);
    }
  }

  // Create a test monitor if the monitors table is empty
  try {
    const { data, error } = await supabase
      .from(MONITOR_TABLE)
      .select('count(*)', { count: 'exact', head: true });

    if (!error && data[0].count === 0) {
      console.log('\nMonitors table is empty. Creating a test monitor...');

      const testMonitor = {
        name: 'Test Monitor',
        type: 'http',
        target: 'https://www.google.com',
        interval: 5,
        timeout: 30,
        active: true
      };

      const { error: insertError } = await supabase
        .from(MONITOR_TABLE)
        .insert(testMonitor);

      if (insertError) {
        console.log(`Error creating test monitor: ${insertError.message}`);
      } else {
        console.log('Successfully created test monitor!');
      }
    }
  } catch (error) {
    console.log(`Error checking if monitors table is empty: ${error.message}`);
  }

  console.log('\n=== TABLE CREATION COMPLETE ===');
}

createTables().catch(error => {
  console.error('Unhandled error:', error);
});
