// <PERSON>ript to force a status change for a monitor
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

async function forceStatusChange() {
  try {
    console.log('Forcing status change for a monitor...');
    
    // Get a monitor
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true)
      .limit(1);
    
    if (error) {
      throw error;
    }
    
    if (!monitors || monitors.length === 0) {
      console.log('No active monitors found');
      return;
    }
    
    const monitor = monitors[0];
    console.log(`Selected monitor: ${monitor.name} (ID: ${monitor.id})`);
    
    // Get the current status
    const { data: latestCheck, error: checkError } = await supabase
      .from('monitor_checks')
      .select('*')
      .eq('monitor_id', monitor.id)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (checkError) {
      throw checkError;
    }
    
    let currentStatus = 'unknown';
    if (latestCheck && latestCheck.length > 0) {
      currentStatus = latestCheck[0].status;
    }
    
    console.log(`Current status: ${currentStatus}`);
    
    // Determine the new status (toggle between up and down)
    const newStatus = currentStatus === 'up' ? 'down' : 'up';
    console.log(`New status: ${newStatus}`);
    
    // Insert a new check with the new status
    const { data: newCheck, error: insertError } = await supabase
      .from('monitor_checks')
      .insert([
        {
          monitor_id: monitor.id,
          status: newStatus,
          response_time: 100,
          details: `Forced status change from ${currentStatus} to ${newStatus}`
        }
      ])
      .select();
    
    if (insertError) {
      throw insertError;
    }
    
    console.log(`Successfully inserted new check with status ${newStatus}`);
    console.log('Wait for the monitor service to detect this change and send notifications');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

forceStatusChange();
