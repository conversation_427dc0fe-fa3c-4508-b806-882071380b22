# Running VUM Monitor Service with npm

This document provides instructions on how to run the VUM Monitor Service using npm commands.

## Available npm Commands

The monitor service provides several npm commands for different components and use cases:

### Monitor Service

```bash
# Run the concurrent monitor service (checks multiple monitors simultaneously)
npm run start-concurrent

# Run with auto-restart for development
npm run dev-concurrent
```

### Notification Services

```bash
# Run the notification listener (listens for monitor status changes)
npm run start-notifications

# Run the email notification listener
npm run start-email-listener
```

### Utility Commands

```bash
# Check all monitors immediately
npm run check-monitors

# Test a specific monitor by ID
npm run check-monitor -- --id=your_monitor_id

# Check a specific website directly
npm run check-website -- --url=https://example.com

# Test database connection
npm run diagnose

# Test email notifications
npm run test-email

# Send a test email directly
npm run send-email
```

## Keeping the Service Running

To keep the service running in the background:

### Method 1: Using a Terminal Multiplexer

#### Using Screen

1. Install screen if not already installed:
   ```bash
   # On Ubuntu/Debian
   sudo apt-get install screen

   # On macOS with Homebrew
   brew install screen
   ```

2. Start a new screen session:
   ```bash
   screen -S vum-monitor
   ```

3. Run the monitor service:
   ```bash
   npm run start-concurrent
   ```

4. Detach from the screen session by pressing `Ctrl+A` followed by `D`

5. To reattach to the session later:
   ```bash
   screen -r vum-monitor
   ```

#### Using Tmux

1. Install tmux if not already installed:
   ```bash
   # On Ubuntu/Debian
   sudo apt-get install tmux

   # On macOS with Homebrew
   brew install tmux
   ```

2. Start a new tmux session:
   ```bash
   tmux new -s vum-monitor
   ```

3. Run the monitor service:
   ```bash
   npm run start-concurrent
   ```

4. Detach from the tmux session by pressing `Ctrl+B` followed by `D`

5. To reattach to the session later:
   ```bash
   tmux attach -t vum-monitor
   ```

### Method 2: Using nohup

```bash
# Run the service in the background
nohup npm run start-concurrent > monitor-output.log 2>&1 &

# To check the process
ps aux | grep node

# To stop the service
kill <process_id>
```

## Configuration

Make sure to configure your `.env` file with the correct settings:

```
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your_supabase_anon_key

# Database Connection (for notification listener)
DB_HOST=db.your-project-id.supabase.co
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your_database_password

# Monitor Service Configuration
CHECK_INTERVAL=60000  # Check every 60 seconds (60000 ms)
LOG_LEVEL=info        # Log level (debug, info, warn, error)
MONITOR_TABLE=monitors
HISTORY_TABLE=monitor_history
NOTIFICATION_TABLE=notifications
MAX_CONCURRENT_CHECKS=10  # Maximum number of concurrent checks
```

## Logs

Logs are stored in the `logs` directory:

- `monitor-service.log`: Contains all log messages
- `error.log`: Contains only error messages

You can view the logs in real-time using:

```bash
tail -f logs/monitor-service.log
```
