# VUM Monitor Service - Deployment Guide

## 🚀 Quick Deployment

### Step 1: Build the Deployment Package
```bash
npm run build
```

This creates a deployment-ready package in the `build/` directory with:
- ✅ All necessary files
- ✅ Setup scripts for Windows and Linux/Mac
- ✅ Configuration templates
- ✅ Documentation
- ✅ Compressed zip file for easy transfer

### Step 2: Transfer to Target Server
1. Copy the entire `build/vum-monitor-service-deployment/` folder to your target server
2. Or copy the zip file `vum-monitor-service-deployment-YYYY-MM-DD.zip` and extract it

### Step 3: Setup on Target Server

#### Windows:
```cmd
# Navigate to the deployment folder
cd vum-monitor-service-deployment

# Run setup (installs dependencies)
setup.bat

# Configure environment
copy .env.example .env
# Edit .env with your settings

# Create database tables
npm run create-tables

# Start the service
start.bat
```

#### Linux/Mac:
```bash
# Navigate to the deployment folder
cd vum-monitor-service-deployment

# Run setup (installs dependencies)
./setup.sh

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Create database tables
npm run create-tables

# Start the service
./start.sh
```

## 📋 What's Included in the Deployment Package

### Core Service Files
- `concurrent-monitor-service.js` - Main monitoring service
- `notification-listener.js` - Email notification handler
- `checker.js` - Monitor checking logic
- `database.js` - Database operations
- `logger.js` - Logging functionality

### Configuration
- `.env.example` - Environment configuration template
- `package.json` - Dependencies and scripts
- Setup scripts for Windows and Unix systems

### Utility Scripts
- Manual check tools
- Database diagnostics
- Service management tools
- Email testing utilities

### Documentation
- `DEPLOYMENT-README.md` - Quick start guide
- `README.md` - Full documentation
- `README-NOTIFICATIONS.md` - Email setup guide
- `RUNNING-WITH-NPM.md` - NPM commands reference

## ⚙️ Configuration Requirements

### Required Environment Variables
```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your_service_role_key_here

# Database Connection
DB_HOST=db.your-project-id.supabase.co
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your_database_password_here

# Service Configuration
CHECK_INTERVAL=60000
LOG_LEVEL=info
MAX_CONCURRENT_CHECKS=10
```

### Optional Email Configuration
```env
# Email Notifications (optional)
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=Vurbis Uptime Monitor <<EMAIL>>
ENABLE_EMAIL_ALERTS=true
```

## 🔧 Post-Deployment Setup

### 1. Verify Installation
```bash
npm run check-env          # Check configuration
npm run diagnose           # Test database connection
npm run status             # Check service status
```

### 2. Create Database Tables
```bash
npm run create-tables      # Creates monitors, monitor_history, notifications tables
```

### 3. Test Functionality
```bash
npm run check-monitors     # List existing monitors
npm run test-connection    # Test database connectivity
```

### 4. Start Services
```bash
# Main monitoring service
npm start

# Email notifications (optional, in separate terminal)
npm run start-notifications
```

## 🖥️ Service Management

### Windows Service Installation
```cmd
# Install as Windows service
npm run install-win-service

# Uninstall Windows service
npm run uninstall-win-service
```

### Manual Service Control
```bash
# Start monitoring service
npm start

# Start notification listener
npm run start-notifications

# Check service status
npm run status
```

## 📊 Monitoring and Logs

### Log Files
- `logs/monitor-service.log` - Main service logs
- `logs/error.log` - Error logs only
- `logs/email-notification-listener.log` - Email service logs

### Useful Commands
```bash
# View live logs (Linux/Mac)
tail -f logs/monitor-service.log

# Check recent monitor activity
npm run check-history

# Manual monitor check
npm run manual-check --id=<monitor_id>

# Check all monitors immediately
npm run check-all-monitors
```

## 🔍 Troubleshooting

### Common Issues
1. **Dependencies fail to install**: Check Node.js version (requires 14.x+)
2. **Database connection fails**: Verify Supabase credentials and network access
3. **Service won't start**: Check `.env` configuration and logs
4. **Monitors not checking**: Verify database tables exist and monitors are active

### Diagnostic Commands
```bash
npm run check-env          # Verify environment configuration
npm run diagnose           # Database connectivity test
npm run find-table         # Locate monitor tables
npm run check-rls          # Check database permissions
```

## 📦 Build Information

The deployment package includes:
- **49 files** copied from source
- **0.08 MB** compressed size
- Cross-platform compatibility (Windows, Linux, Mac)
- Self-contained with all dependencies listed in package.json

## 🆘 Support

- **GitHub Repository**: https://github.com/RayZwankhuizen/VUM-Backend
- **Documentation**: Check README files in the deployment package
- **Logs**: Always check logs first for error details
- **Diagnostics**: Use built-in diagnostic tools before reporting issues
