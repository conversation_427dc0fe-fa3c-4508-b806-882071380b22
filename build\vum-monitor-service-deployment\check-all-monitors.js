// <PERSON>ript to manually check all monitors
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { checkMonitor } = require('./checker');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Main function
async function main() {
  try {
    log(`Starting manual check for all monitors`);

    // Get all active monitors from the database
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw new Error(`Failed to get monitors: ${error.message}`);
    }

    if (!monitors || monitors.length === 0) {
      throw new Error(`No active monitors found`);
    }

    log(`Found ${monitors.length} active monitors`);

    // Check each monitor with isManualCheck flag set to true
    for (const monitor of monitors) {
      log(`Checking monitor: ${monitor.name} (${monitor.type})`);
      const result = await checkMonitor(monitor, true);
      log(`Check completed for ${monitor.name}: ${result.status ? 'UP' : 'DOWN'} (${result.response_time}ms)`);
      
      if (result.error_message) {
        log(`Error: ${result.error_message}`, 'WARN');
      }
    }

    log(`All monitors checked successfully`);
    process.exit(0);
  } catch (error) {
    log(`Error performing manual check: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run the main function
main();
