// Test script for the email notification system
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'test-notification.log');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Function to test the email notification
async function testEmailNotification(monitorId = null, status = 'down', companyId = null) {
  try {
    log('Starting email notification test...');

    // If no monitor ID is provided, get the first active monitor
    if (!monitorId) {
      log('No monitor ID provided, finding an active monitor...');

      const { data: monitors, error: monitorError } = await supabase
        .from('monitors')
        .select('id, name')
        .eq('active', true)
        .limit(1);

      if (monitorError) {
        throw new Error(`Error fetching monitors: ${monitorError.message}`);
      }

      if (!monitors || monitors.length === 0) {
        throw new Error('No active monitors found. Please create a monitor first.');
      }

      monitorId = monitors[0].id;
      log(`Using monitor: ${monitors[0].name} (${monitorId})`);
    }

    // If no company ID is provided, get the first company associated with the monitor
    if (!companyId) {
      log('No company ID provided, finding an associated company...');

      const { data: monitorCompanies, error: companiesError } = await supabase
        .from('monitor_companies')
        .select('company_id')
        .eq('monitor_id', monitorId)
        .limit(1);

      if (companiesError) {
        throw new Error(`Error fetching monitor companies: ${companiesError.message}`);
      }

      if (!monitorCompanies || monitorCompanies.length === 0) {
        // If no company is associated, get the first company
        log('No companies associated with this monitor, finding any company...');

        const { data: companies, error: allCompaniesError } = await supabase
          .from('companies')
          .select('id, name')
          .limit(1);

        if (allCompaniesError) {
          throw new Error(`Error fetching companies: ${allCompaniesError.message}`);
        }

        if (!companies || companies.length === 0) {
          throw new Error('No companies found. Please create a company first.');
        }

        companyId = companies[0].id;
        log(`Using company: ${companies[0].name} (${companyId})`);
      } else {
        companyId = monitorCompanies[0].company_id;

        // Get company name for logging
        const { data: company, error: companyError } = await supabase
          .from('companies')
          .select('name')
          .eq('id', companyId)
          .single();

        if (!companyError && company) {
          log(`Using company: ${company.name} (${companyId})`);
        } else {
          log(`Using company ID: ${companyId}`);
        }
      }
    }

    // Validate status
    if (!['up', 'down', 'degraded'].includes(status)) {
      log(`Invalid status: ${status}, defaulting to 'down'`, 'WARN');
      status = 'down';
    }

    // Create a test monitor history record
    log(`Creating test monitor history record with status: ${status}...`);

    const { data: historyData, error: historyError } = await supabase
      .from('monitor_history')
      .insert({
        monitor_id: monitorId,
        status: status,
        response_time: status === 'up' ? 200 : null,
        error_message: status === 'up' ? null : 'Test error message',
        timestamp: new Date().toISOString()
      })
      .select();

    if (historyError) {
      throw new Error(`Error creating monitor history record: ${historyError.message}`);
    }

    log('Test monitor history record created successfully.');

    // Directly call the Edge Function to ensure email is sent
    log('Calling Edge Function to send email notification...');

    const response = await axios.post(
      `${SUPABASE_URL}/functions/v1/send-monitor-notification`,
      {
        monitor_id: monitorId,
        status: status,
        company_id: companyId
      },
      {
        headers: {
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200) {
      log(`Email notification sent successfully: ${JSON.stringify(response.data)}`);
      return true;
    } else {
      throw new Error(`Failed to send email notification: ${response.statusText}`);
    }
  } catch (error) {
    log(`Error in test: ${error.message}`, 'ERROR');
    return false;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
let monitorId = null;
let status = 'down';
let companyId = null;

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--monitor' && i + 1 < args.length) {
    monitorId = args[i + 1];
    i++;
  } else if (args[i] === '--status' && i + 1 < args.length) {
    status = args[i + 1];
    i++;
  } else if (args[i] === '--company' && i + 1 < args.length) {
    companyId = args[i + 1];
    i++;
  }
}

// Run the test
testEmailNotification(monitorId, status, companyId)
  .then(success => {
    if (success) {
      log('Test completed successfully!');
    } else {
      log('Test failed.', 'ERROR');
    }
  })
  .catch(error => {
    log(`Unexpected error: ${error.message}`, 'ERROR');
  });
