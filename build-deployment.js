// Build deployment package for VUM Monitor Service
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const BUILD_DIR = './build';
const PACKAGE_NAME = 'vum-monitor-service-deployment';

// Files and directories to include in the deployment
const INCLUDE_FILES = [
  // Core service files (ESSENTIAL)
  'concurrent-monitor-service.js',
  'notification-listener.js',
  'checker.js',
  'database.js',
  'logger.js',

  // Configuration and setup (ESSENTIAL)
  'package.json',
  '.env',  // Include actual .env file, not example

  // Essential utilities
  'check-env.js',
  'create-monitor-table.js',
  'check-service-status.js',

  // Manual check (for dashboard integration)
  'concurrent-manual-check.js',

  // Service management (Windows)
  'install-service.js',
  'uninstall-service.js',

  // Basic documentation
  'README.md'
];

// Directories to create
const INCLUDE_DIRS = [
  'logs'
];

console.log('🚀 Building VUM Monitor Service deployment package...\n');

// Check if .env file exists
if (!fs.existsSync('.env')) {
  console.error('❌ ERROR: .env file not found!');
  console.error('Please create a .env file with your configuration before building.');
  console.error('You can copy .env.example to .env and edit it with your settings.');
  process.exit(1);
}

console.log('✅ .env file found');

// Clean and create build directory
if (fs.existsSync(BUILD_DIR)) {
  console.log('📁 Cleaning existing build directory...');
  fs.rmSync(BUILD_DIR, { recursive: true, force: true });
}

fs.mkdirSync(BUILD_DIR, { recursive: true });
console.log('📁 Created build directory');

// Create package directory
const packageDir = path.join(BUILD_DIR, PACKAGE_NAME);
fs.mkdirSync(packageDir, { recursive: true });

// Copy files
console.log('\n📋 Copying files...');
let copiedFiles = 0;
let skippedFiles = 0;

INCLUDE_FILES.forEach(file => {
  const sourcePath = path.join('.', file);
  const destPath = path.join(packageDir, file);

  if (fs.existsSync(sourcePath)) {
    // Create directory if it doesn't exist
    const destDir = path.dirname(destPath);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }

    fs.copyFileSync(sourcePath, destPath);
    console.log(`✅ ${file}`);
    copiedFiles++;
  } else {
    console.log(`⚠️  ${file} (not found, skipping)`);
    skippedFiles++;
  }
});

// Create directories
console.log('\n📂 Creating directories...');
INCLUDE_DIRS.forEach(dir => {
  const dirPath = path.join(packageDir, dir);
  fs.mkdirSync(dirPath, { recursive: true });
  console.log(`✅ ${dir}/`);
});

// Create deployment scripts
console.log('\n📝 Creating deployment scripts...');

// Create setup script for Windows
const setupScriptWindows = `@echo off
echo VUM Monitor Service - Setup Script
echo ====================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found:
node --version

echo.
echo Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Setup completed successfully!
echo.
echo Next steps:
echo 1. Copy .env.example to .env
echo 2. Edit .env with your configuration
echo 3. Run: npm run create-tables
echo 4. Run: npm start
echo.
pause
`;

fs.writeFileSync(path.join(packageDir, 'setup.bat'), setupScriptWindows);
console.log('✅ setup.bat');

// Create setup script for Linux/Mac
const setupScriptUnix = `#!/bin/bash
echo "VUM Monitor Service - Setup Script"
echo "===================================="
echo

echo "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js found: $(node --version)"

echo
echo "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "Setup completed successfully!"
echo
echo "Next steps:"
echo "1. Copy .env.example to .env"
echo "2. Edit .env with your configuration"
echo "3. Run: npm run create-tables"
echo "4. Run: npm start"
echo
`;

fs.writeFileSync(path.join(packageDir, 'setup.sh'), setupScriptUnix);
fs.chmodSync(path.join(packageDir, 'setup.sh'), '755');
console.log('✅ setup.sh');

// Create start script for Windows
const startScriptWindows = `@echo off
echo Starting VUM Monitor Service...
npm start
`;

fs.writeFileSync(path.join(packageDir, 'start.bat'), startScriptWindows);
console.log('✅ start.bat');

// Create start script for Linux/Mac
const startScriptUnix = `#!/bin/bash
echo "Starting VUM Monitor Service..."
npm start
`;

fs.writeFileSync(path.join(packageDir, 'start.sh'), startScriptUnix);
fs.chmodSync(path.join(packageDir, 'start.sh'), '755');
console.log('✅ start.sh');

// Create deployment README
const deploymentReadme = `# VUM Monitor Service - Production Deployment

This is a minimal, production-ready package of the VUM Monitor Service.

## Quick Start

### Windows
1. Run \`setup.bat\` to install dependencies
2. Verify \`.env\` file has your correct settings
3. Run \`npm run create-tables\` to set up database tables (if needed)
4. Run \`start.bat\` to start the service

### Linux/Mac
1. Run \`./setup.sh\` to install dependencies
2. Verify \`.env\` file has your correct settings
3. Run \`npm run create-tables\` to set up database tables (if needed)
4. Run \`./start.sh\` to start the service

## What's Included

This minimal package includes only essential files:
- **Core Service**: concurrent-monitor-service.js, checker.js, database.js, logger.js
- **Notifications**: notification-listener.js
- **Configuration**: .env (your actual config), package.json
- **Setup Tools**: create-monitor-table.js, check-env.js
- **Manual Checks**: concurrent-manual-check.js (for dashboard integration)
- **Service Management**: Windows service install/uninstall scripts

## Available Commands

- \`npm start\` - Start the monitor service
- \`npm run start-notifications\` - Start notification listener
- \`npm run check-env\` - Check environment configuration
- \`npm run create-tables\` - Create database tables
- \`npm run status\` - Check service status
- \`npm run concurrent-manual-check --id=<monitor_id>\` - Manual check for dashboard

## Service Management (Windows)

- \`npm run install-win-service\` - Install as Windows service
- \`npm run uninstall-win-service\` - Uninstall Windows service

## Logs

Logs are stored in the \`logs/\` directory:
- \`monitor-service.log\` - Main service logs
- \`error.log\` - Error logs only

## Support

For issues and documentation, visit: https://github.com/RayZwankhuizen/VUM-Backend
`;

fs.writeFileSync(path.join(packageDir, 'DEPLOYMENT-README.md'), deploymentReadme);
console.log('✅ DEPLOYMENT-README.md');

// Create archive
console.log('\n📦 Creating deployment archive...');

try {
  const archiveName = `${PACKAGE_NAME}-${new Date().toISOString().split('T')[0]}.zip`;
  const archivePath = path.join(BUILD_DIR, archiveName);

  // Use PowerShell on Windows to create zip
  if (process.platform === 'win32') {
    const powershellCmd = `Compress-Archive -Path "${packageDir}\\*" -DestinationPath "${archivePath}" -Force`;
    execSync(`powershell -Command "${powershellCmd}"`, { stdio: 'inherit' });
  } else {
    // Use zip command on Unix systems
    execSync(`cd "${BUILD_DIR}" && zip -r "${archiveName}" "${PACKAGE_NAME}"`, { stdio: 'inherit' });
  }

  console.log(`✅ Created: ${archiveName}`);

  // Get file size
  const stats = fs.statSync(archivePath);
  const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);

  console.log(`📊 Archive size: ${fileSizeInMB} MB`);

} catch (error) {
  console.log('⚠️  Could not create archive automatically. You can manually zip the folder.');
  console.log('Error:', error.message);
}

// Summary
console.log('\n🎉 Build completed successfully!');
console.log('\n📋 Summary:');
console.log(`   Files copied: ${copiedFiles}`);
console.log(`   Files skipped: ${skippedFiles}`);
console.log(`   Package location: ${packageDir}`);
console.log('\n📝 Next steps:');
console.log('   1. Copy the build folder to your target server');
console.log('   2. Run the setup script on the target server');
console.log('   3. Configure the .env file');
console.log('   4. Start the service');
console.log('\n✨ Ready for deployment!');
