#!/bin/bash
echo "VUM Monitor Service - Setup Script"
echo "===================================="
echo

echo "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js found: $(node --version)"

echo
echo "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "Setup completed successfully!"
echo
echo "Next steps:"
echo "1. Copy .env.example to .env"
echo "2. Edit .env with your configuration"
echo "3. Run: npm run create-tables"
echo "4. Run: npm start"
echo
