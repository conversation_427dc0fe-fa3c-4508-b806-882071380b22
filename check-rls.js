// <PERSON><PERSON><PERSON> to check for RLS (Row Level Security) issues
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const MONITOR_TABLE = process.env.MONITOR_TABLE || 'monitors';

// Initialize Supabase client with anon key
const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkRLS() {
  console.log('=== RLS (ROW LEVEL SECURITY) CHECK ===');
  console.log(`Supabase URL: ${SUPABASE_URL}`);
  console.log(`Supabase Key: ${SUPABASE_KEY.substring(0, 10)}...${SUPABASE_KEY.substring(SUPABASE_KEY.length - 5)}`);
  console.log(`Monitor Table: ${MONITOR_TABLE}`);

  // Check if we can read from the monitors table
  console.log('\n=== CHECKING READ ACCESS ===');
  try {
    console.log(`Attempting to read from '${MONITOR_TABLE}' table...`);

    const { data, error } = await supabaseAnon
      .from(MONITOR_TABLE)
      .select('*')
      .limit(5);

    if (error) {
      console.log(`Error: ${error.message}`);
      console.log('This suggests there might be RLS policies preventing read access.');
    } else {
      console.log(`Success! Found ${data ? data.length : 0} records.`);

      if (data && data.length > 0) {
        console.log('\nFirst record:');
        console.log(data[0]);
      } else {
        console.log('No records found. This could be because:');
        console.log('1. The table is empty');
        console.log('2. RLS policies are preventing access to records');
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  // Check if we can insert into the monitors table
  console.log('\n=== CHECKING INSERT ACCESS ===');
  try {
    console.log(`Attempting to insert into '${MONITOR_TABLE}' table...`);

    const testRecord = {
      name: 'RLS Test Monitor',
      type: 'http',
      target: 'https://www.example.com',
      interval: 5,
      timeout: 30,
      active: true
    };

    const { data, error } = await supabaseAnon
      .from(MONITOR_TABLE)
      .insert(testRecord)
      .select();

    if (error) {
      console.log(`Error: ${error.message}`);
      console.log('This suggests there might be RLS policies preventing insert access.');
    } else {
      console.log('Insert successful!');

      if (data && data.length > 0) {
        console.log('\nInserted record:');
        console.log(data[0]);

        // Try to delete the test record
        console.log('\nAttempting to delete the test record...');

        const { error: deleteError } = await supabaseAnon
          .from(MONITOR_TABLE)
          .delete()
          .eq('id', data[0].id);

        if (deleteError) {
          console.log(`Delete Error: ${deleteError.message}`);
          console.log('This suggests there might be RLS policies preventing delete access.');
        } else {
          console.log('Delete successful!');
        }
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  // Check RLS policies
  console.log('\n=== CHECKING RLS POLICIES ===');
  try {
    console.log(`Attempting to check RLS policies for '${MONITOR_TABLE}' table...`);

    const { data, error } = await supabaseAnon.rpc('get_policies', {
      table_name: MONITOR_TABLE
    });

    if (error) {
      console.log(`Error: ${error.message}`);
      console.log('Could not check RLS policies. This function might not be available.');

      // Try alternative approach
      console.log('\nTrying alternative approach...');

      const { data: pgPolicies, error: pgError } = await supabaseAnon
        .from('pg_policies')
        .select('*')
        .ilike('tablename', MONITOR_TABLE);

      if (pgError) {
        console.log(`Error: ${pgError.message}`);
        console.log('Could not access pg_policies. This might require admin privileges.');
      } else {
        console.log(`Found ${pgPolicies ? pgPolicies.length : 0} policies.`);

        if (pgPolicies && pgPolicies.length > 0) {
          console.log('\nPolicies:');
          pgPolicies.forEach(policy => {
            console.log(policy);
          });
        } else {
          console.log('No policies found. This could mean:');
          console.log('1. There are no RLS policies on this table');
          console.log('2. You do not have permission to view policies');
        }
      }
    } else {
      console.log(`Found ${data ? data.length : 0} policies.`);

      if (data && data.length > 0) {
        console.log('\nPolicies:');
        data.forEach(policy => {
          console.log(policy);
        });
      } else {
        console.log('No policies found. This suggests there are no RLS policies on this table.');
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  // Check if RLS is enabled for the table
  console.log('\n=== CHECKING IF RLS IS ENABLED ===');
  try {
    console.log(`Checking if RLS is enabled for '${MONITOR_TABLE}' table...`);

    const { data, error } = await supabaseAnon.rpc('check_rls_enabled', {
      table_name: MONITOR_TABLE
    });

    if (error) {
      console.log(`Error: ${error.message}`);
      console.log('Could not check if RLS is enabled. This function might not be available.');

      // Try alternative approach
      console.log('\nTrying alternative approach...');

      const { data: pgTables, error: pgError } = await supabaseAnon
        .from('pg_tables')
        .select('*')
        .eq('tablename', MONITOR_TABLE);

      if (pgError) {
        console.log(`Error: ${pgError.message}`);
        console.log('Could not access pg_tables. This might require admin privileges.');
      } else {
        console.log(`Found ${pgTables ? pgTables.length : 0} tables.`);

        if (pgTables && pgTables.length > 0) {
          console.log('\nTable info:');
          console.log(pgTables[0]);
        }
      }
    } else {
      console.log(`RLS enabled: ${data}`);

      if (data) {
        console.log('RLS is enabled for this table. This means access is restricted by policies.');
      } else {
        console.log('RLS is not enabled for this table. This means access is not restricted by policies.');
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== RLS CHECK COMPLETE ===');
  console.log('\nRecommendations:');
  console.log('1. If you cannot read records but know they exist, RLS policies might be preventing access.');
  console.log('2. Check the Supabase dashboard for RLS policies on the monitors table.');
  console.log('3. You might need to use a service role key instead of the anon key.');
  console.log('4. If using the anon key, make sure there are appropriate RLS policies for anonymous access.');
}

checkRLS().catch(error => {
  console.error('Unhandled error:', error);
});
