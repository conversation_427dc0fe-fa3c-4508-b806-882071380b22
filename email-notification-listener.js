// Email Notification Listener Service
// This service listens for database notifications and sends email notifications
const { createClient } = require('@supabase/supabase-js');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the direct email sender
const { sendEmailNotification } = require('./direct-email-sender');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const DB_HOST = process.env.DB_HOST;
const DB_PORT = process.env.DB_PORT || 5432;
const DB_NAME = process.env.DB_NAME || 'postgres';
const DB_USER = process.env.DB_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

if (!DB_HOST || !DB_PASSWORD) {
  console.error('ERROR: Required environment variables DB_HOST and/or DB_PASSWORD are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'email-notification-listener.log');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Create a PostgreSQL connection pool
const pool = new Pool({
  host: DB_HOST,
  port: DB_PORT,
  database: DB_NAME,
  user: DB_USER,
  password: DB_PASSWORD,
  ssl: true
});

// Start the notification listener
async function startListener() {
  log('Starting email notification listener service...');

  try {
    // Test Supabase connection
    const { data, error, count } = await supabase
      .from('monitors')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    log(`Connected to Supabase. Found ${count} monitors.`);

    // Connect to the database for notifications
    const client = await pool.connect();

    // Listen for notifications
    await client.query('LISTEN monitor_email_notification');

    log('Listening for monitor email notifications...');

    // Set up notification handler
    client.on('notification', async (notification) => {
      try {
        // Parse the payload
        const payload = JSON.parse(notification.payload);
        log(`Received notification: ${JSON.stringify(payload)}`);

        const { monitor_id, status, company_id } = payload;

        if (!monitor_id || !status || !company_id) {
          log('Invalid notification payload', 'ERROR');
          return;
        }

        // Get monitor details
        const { data: monitor, error: monitorError } = await supabase
          .from('monitors')
          .select('*')
          .eq('id', monitor_id)
          .single();

        if (monitorError) {
          log(`Error getting monitor details: ${monitorError.message}`, 'ERROR');
          return;
        }

        // Send email notification
        const success = await sendEmailNotification(monitor_id, status, company_id);

        if (success) {
          log(`Email notification sent successfully for monitor ${monitor.name}`);

          // Mark the notification as processed
          const { error: updateError } = await supabase
            .from('monitor_email_notifications')
            .update({
              processed: true,
              processed_at: new Date().toISOString()
            })
            .eq('monitor_id', monitor_id)
            .eq('company_id', company_id)
            .eq('processed', false);

          if (updateError) {
            log(`Error marking notification as processed: ${updateError.message}`, 'WARN');
          }
        } else {
          log(`Failed to send email notification for monitor ${monitor.name}`, 'ERROR');
        }
      } catch (error) {
        log(`Error processing notification: ${error.message}`, 'ERROR');
      }
    });

    // Keep the connection alive
    setInterval(() => {
      client.query('SELECT 1');
    }, 60000);

    // Handle errors
    client.on('error', (err) => {
      log(`Database connection error: ${err.message}`, 'ERROR');
      setTimeout(startListener, 10000); // Restart after 10 seconds
    });

    log('Email notification listener service started successfully');
  } catch (error) {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    log('Retrying in 30 seconds...');

    // Retry after a delay
    setTimeout(startListener, 30000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('Shutting down email notification listener service...');
  pool.end();
  log('Email notification listener service stopped');
  process.exit(0);
});

// Start the service
startListener();
