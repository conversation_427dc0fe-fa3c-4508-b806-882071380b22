# Supabase Configuration
# Get these values from your Supabase dashboard: Project Settings > API
SUPABASE_URL=https://your-project-id.supabase.co

# IMPORTANT: SUPABASE_KEY must be the service_role key from Supabase
# The service_role key bypasses RLS policies and has full access to all monitors
# Get this from your Supabase dashboard: Project Settings > API > service_role key
SUPABASE_KEY=your_service_role_key_here

# Database Connection (for notification listener)
# These credentials are needed for the PostgreSQL notification listener
DB_HOST=db.your-project-id.supabase.co
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
# Replace with your actual database password from Supabase dashboard > Project Settings > Database
DB_PASSWORD=your_database_password_here

# Monitor Service Configuration
CHECK_INTERVAL=60000  # Check every 60 seconds (60000 ms)
LOG_LEVEL=info        # Log level (debug, info, warn, error)
MONITOR_TABLE=monitors
HISTORY_TABLE=monitor_history
NOTIFICATION_TABLE=notifications
MAX_CONCURRENT_CHECKS=10  # Maximum number of concurrent checks

# Monitor Service User (should be a superadmin)
# This user has been created in Supabase and made a superadmin
MONITOR_SERVICE_EMAIL=<EMAIL>
MONITOR_SERVICE_PASSWORD=your_secure_password_here

# Email Configuration
# Get your API key from https://resend.com
RESEND_API_KEY=your_resend_api_key_here
RESEND_API_URL=https://api.resend.com/emails
RESEND_FROM_EMAIL=Vurbis Uptime Monitor <<EMAIL>>
ENABLE_EMAIL_ALERTS=false  # Set to true to enable email alerts
