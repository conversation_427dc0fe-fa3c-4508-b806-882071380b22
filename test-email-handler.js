// Test Email Handler
// This script handles test email requests
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'test-email-handler.log');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Function to send a test email
async function sendTestEmail(monitorId, email, status, companyId) {
  try {
    log(`Preparing to send test email for monitor ${monitorId} (${status}) to ${email}`);

    // Get monitor details
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('name, target, type')
      .eq('id', monitorId)
      .single();

    if (monitorError) {
      throw new Error(`Failed to get monitor details: ${monitorError.message}`);
    }

    log(`Monitor details: ${JSON.stringify(monitor)}`);

    // Get company details
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('name')
      .eq('id', companyId)
      .single();

    if (companyError) {
      throw new Error(`Failed to get company details: ${companyError.message}`);
    }

    log(`Company details: ${JSON.stringify(company)}`);

    // Create email content
    const statusText = status === 'up' ? 'UP' : status === 'down' ? 'DOWN' : 'DEGRADED';
    const statusColor = status === 'up' ? '#4CAF50' : status === 'down' ? '#F44336' : '#FF9800';

    const subject = `[TEST] ${company.name} Monitor ${monitor.name} is ${statusText}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">TEST NOTIFICATION - Monitor Status Change</h2>
        <div style="padding: 20px; border-radius: 5px; margin-bottom: 20px; background-color: ${statusColor}; color: white;">
          <h3 style="margin-top: 0;">Monitor is now ${statusText}</h3>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>Company:</strong> ${company.name}</p>
          <p><strong>Monitor:</strong> ${monitor.name}</p>
          <p><strong>Type:</strong> ${monitor.type}</p>
          <p><strong>Target:</strong> ${monitor.target}</p>
          <p><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 30px;">
          <p>This is a TEST email from Vurbis Uptime Monitor.</p>
        </div>
      </div>
    `;

    // Check if RESEND_API_KEY is set
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not set in .env file');
    }

    log(`RESEND_API_KEY is set: ${RESEND_API_KEY ? 'Yes' : 'No'}`);
    log(`RESEND_API_KEY length: ${RESEND_API_KEY ? RESEND_API_KEY.length : 0}`);
    log(`Sending test email to ${email}`);

    // Send email using Resend API
    try {
      log('Making request to Resend API...');
      const response = await axios.post('https://api.resend.com/emails', {
        from: 'Vurbis Uptime Monitor <<EMAIL>>',
        to: email,
        subject,
        html,
      }, {
        headers: {
          'Authorization': `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      log(`Resend API response: ${JSON.stringify(response.data)}`);

      if (response.status === 200) {
        log(`Test email sent successfully to ${email}`);
        return true;
      } else {
        throw new Error(`Failed to send test email: ${response.statusText}`);
      }
    } catch (axiosError) {
      if (axiosError.response) {
        log(`Resend API error response: ${JSON.stringify(axiosError.response.data)}`, 'ERROR');
        throw new Error(`Resend API error: ${axiosError.response.status} - ${JSON.stringify(axiosError.response.data)}`);
      } else if (axiosError.request) {
        log('No response received from Resend API', 'ERROR');
        throw new Error('No response received from Resend API');
      } else {
        throw axiosError;
      }
    }
  } catch (error) {
    log(`Error sending test email: ${error.message}`, 'ERROR');
    return false;
  }
}

// Function to process email requests
async function processEmailRequests() {
  try {
    // Get unprocessed email requests
    const { data: requests, error } = await supabase
      .from('test_email_requests')
      .select('id, monitor_id, email, status, company_id')
      .eq('processed', false)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    if (!requests || requests.length === 0) {
      return; // No requests to process
    }

    log(`Found ${requests.length} unprocessed email requests`);

    for (const request of requests) {
      try {
        const { id, monitor_id, email, status, company_id } = request;

        log(`Processing email request: ${JSON.stringify(request)}`);

        // Send test email
        const success = await sendTestEmail(monitor_id, email, status, company_id);

        // Mark the request as processed
        const { error: updateError } = await supabase
          .from('test_email_requests')
          .update({
            processed: true,
            processed_at: new Date().toISOString()
          })
          .eq('id', id);

        if (updateError) {
          log(`Error updating request status: ${updateError.message}`, 'ERROR');
        }

        if (success) {
          log(`Test email sent successfully to ${email}`);
        } else {
          log(`Failed to send test email to ${email}`, 'ERROR');
        }
      } catch (error) {
        log(`Error processing request: ${error.message}`, 'ERROR');
        // Move to the next request
        continue;
      }
    }
  } catch (error) {
    log(`Error processing email requests: ${error.message}`, 'ERROR');
  }
}

// Start the handler
async function startHandler() {
  log('Starting test email handler...');

  try {
    // Test Supabase connection
    const { data, error, count } = await supabase
      .from('monitors')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    log(`Connected to Supabase. Found ${count} monitors.`);

    // Process any existing requests
    await processEmailRequests();

    // Set up a timer to check for new requests periodically
    setInterval(processEmailRequests, 5000); // Check every 5 seconds

    log('Test email handler started successfully');
  } catch (error) {
    log(`Failed to start handler: ${error.message}`, 'ERROR');
    log('Retrying in 30 seconds...');

    // Retry after a delay
    setTimeout(startHandler, 30000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('Shutting down test email handler...');
  log('Test email handler stopped');
  process.exit(0);
});

// Start the handler
startHandler();
