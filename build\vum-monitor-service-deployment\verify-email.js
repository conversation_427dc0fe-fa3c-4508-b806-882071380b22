// Email verification script
// This script tests if the email configuration is working correctly
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Load environment variables
require('dotenv').config();

// Configuration
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'verify-email.log');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Function to send a test email
async function sendTestEmail(email) {
  try {
    log(`Starting email verification test...`);
    
    // Check if RESEND_API_KEY is set
    const RESEND_API_KEY = process.env.RESEND_API_KEY;
    
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not set in .env file');
    }
    
    log(`RESEND_API_KEY is set: ${RESEND_API_KEY ? 'Yes' : 'No'}`);
    log(`RESEND_API_KEY length: ${RESEND_API_KEY ? RESEND_API_KEY.length : 0}`);
    log(`RESEND_API_KEY type: ${RESEND_API_KEY ? (RESEND_API_KEY.startsWith('re_test_') ? 'TEST KEY' : 'PRODUCTION KEY') : 'N/A'}`);
    
    // Create email content
    const subject = `VUM Email Test - ${new Date().toLocaleString()}`;
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Email Verification Test</h2>
        <div style="padding: 20px; border-radius: 5px; margin-bottom: 20px; background-color: #4CAF50; color: white;">
          <h3 style="margin-top: 0;">Test Email</h3>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>This is a test email to verify that the email sending functionality is working correctly.</strong></p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 30px;">
          <p>This is a TEST email from Vurbis Uptime Monitor.</p>
        </div>
      </div>
    `;
    
    log(`Sending test email to ${email}`);
    
    // Send email using Resend API
    try {
      log('Making request to Resend API...');
      const response = await axios.post('https://api.resend.com/emails', {
        from: 'Vurbis Uptime Monitor <<EMAIL>>',
        to: email,
        subject,
        html,
      }, {
        headers: {
          'Authorization': `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });
      
      log(`Resend API response: ${JSON.stringify(response.data)}`);
      
      if (response.status === 200) {
        log(`Test email sent successfully to ${email}`);
        return true;
      } else {
        throw new Error(`Failed to send test email: ${response.statusText}`);
      }
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        log(`Error response data: ${JSON.stringify(error.response.data)}`, 'ERROR');
        log(`Error response status: ${error.response.status}`, 'ERROR');
        log(`Error response headers: ${JSON.stringify(error.response.headers)}`, 'ERROR');
      } else if (error.request) {
        // The request was made but no response was received
        log(`Error request: ${error.request}`, 'ERROR');
      } else {
        // Something happened in setting up the request that triggered an Error
        log(`Error message: ${error.message}`, 'ERROR');
      }
      throw error;
    }
  } catch (error) {
    log(`Error sending test email: ${error.message}`, 'ERROR');
    return false;
  }
}

// Main function
async function main() {
  const email = process.argv[2];
  
  if (!email) {
    log('Please provide an email address as a command line argument', 'ERROR');
    log('Usage: node verify-email.js <EMAIL>', 'ERROR');
    process.exit(1);
  }
  
  try {
    const success = await sendTestEmail(email);
    
    if (success) {
      log('Email verification test completed successfully');
      process.exit(0);
    } else {
      log('Email verification test failed', 'ERROR');
      process.exit(1);
    }
  } catch (error) {
    log(`Unexpected error: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run the main function
main();
